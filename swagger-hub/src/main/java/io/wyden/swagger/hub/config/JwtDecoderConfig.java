package io.wyden.swagger.hub.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.oauth2.jwt.NimbusReactiveJwtDecoder;
import org.springframework.security.oauth2.jwt.SupplierReactiveJwtDecoder;

@Configuration
@ConditionalOnProperty("security.jwt.issuer.validation.disabled")
public class JwtDecoderConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(JwtDecoderConfig.class);

    @Bean
    @Primary
    SupplierReactiveJwtDecoder reactiveJwtDecoder(@Value("${spring.security.oauth2.client.provider.keycloak.issuer-uri}") String realmUri) {
        LOGGER.warn("Disabling jwt issuer validation, please do not use this on production.");
        return new SupplierReactiveJwtDecoder(() -> NimbusReactiveJwtDecoder.withJwkSetUri(realmUri + "/protocol/openid-connect/certs").build());
    }
}

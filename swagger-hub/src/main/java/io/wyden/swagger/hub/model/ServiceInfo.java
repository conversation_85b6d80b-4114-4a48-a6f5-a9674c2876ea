package io.wyden.swagger.hub.model;

public class ServiceInfo {
    private String name;
    private String url;
    private String swaggerPath = "/v3/api-docs";
    private String basePath;
    private boolean enabled = true;

    public ServiceInfo() {}

    public ServiceInfo(String name, String url) {
        this.name = name;
        this.url = url;
    }

    public ServiceInfo(String name, String url, String swaggerPath) {
        this.name = name;
        this.url = url;
        this.swaggerPath = swaggerPath;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSwaggerPath() {
        return swaggerPath;
    }

    public void setSwaggerPath(String swaggerPath) {
        this.swaggerPath = swaggerPath;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}

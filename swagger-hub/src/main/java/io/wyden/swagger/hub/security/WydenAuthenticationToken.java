package io.wyden.swagger.hub.security;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;

import java.util.Collection;
import java.util.Collections;

public class WydenAuthenticationToken extends AbstractAuthenticationToken {

    private final Jwt jwt;

    public WydenAuthenticationToken(Jwt jwt) {
        super(Collections.emptyList());
        this.jwt = jwt;
        setAuthenticated(true);
    }

    public WydenAuthenticationToken(Jwt jwt, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.jwt = jwt;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return jwt.getTokenValue();
    }

    @Override
    public Object getPrincipal() {
        return jwt.getSubject();
    }

    public Jwt getJwt() {
        return jwt;
    }
}

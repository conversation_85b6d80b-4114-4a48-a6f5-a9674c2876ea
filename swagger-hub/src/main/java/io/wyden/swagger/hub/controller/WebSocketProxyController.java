package io.wyden.swagger.hub.controller;

import io.wyden.swagger.hub.config.SwaggerServiceConfig;
import io.wyden.swagger.hub.model.ServiceInfo;
import io.wyden.swagger.hub.security.WydenAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketSession;
import org.springframework.web.reactive.socket.client.ReactorNettyWebSocketClient;
import org.springframework.web.reactive.socket.client.WebSocketClient;
import org.springframework.web.reactive.socket.server.support.WebSocketHandlerAdapter;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Optional;

@Component
public class WebSocketProxyController implements WebSocketHandler {

    private static final Logger log = LoggerFactory.getLogger(WebSocketProxyController.class);

    private final SwaggerServiceConfig config;
    private final WebSocketClient webSocketClient;

    public WebSocketProxyController(SwaggerServiceConfig config) {
        this.config = config;
        this.webSocketClient = new ReactorNettyWebSocketClient();
    }

    @Override
    public Mono<Void> handle(WebSocketSession session) {
        // Extract service name from WebSocket path
        String path = session.getHandshakeInfo().getUri().getPath();
        String serviceName = extractServiceName(path);

        if (serviceName == null) {
            log.warn("Invalid WebSocket path: {}", path);
            return session.close();
        }

        // Extract token from query parameters or headers
        String token = extractTokenFromSession(session);

        Optional<ServiceInfo> serviceOpt = config.getServices().stream()
            .filter(s -> s.getName().equals(serviceName))
            .findFirst();

        if (serviceOpt.isEmpty()) {
            log.warn("Service not found for WebSocket: {}", serviceName);
            return session.close();
        }

        ServiceInfo service = serviceOpt.get();
        String targetPath = path.replaceFirst("/ws-proxy/" + serviceName, "");
        String wsUrl = service.getUrl().replace("http://", "ws://").replace("https://", "wss://");
        URI targetUri = URI.create(wsUrl + targetPath);

        log.info("Proxying WebSocket connection from {} to {}", path, targetUri);

        return webSocketClient.execute(targetUri, headers -> {
            // Add Authorization header if token is available
            if (token != null && !token.isEmpty()) {
                headers.add(HttpHeaders.AUTHORIZATION, "Bearer " + token);
            }
        }, targetSession -> {
            // Forward messages from client to target service
            Mono<Void> input = session.receive()
                .doOnNext(message -> log.debug("Client -> Service: {}", message.getPayloadAsText()))
                .flatMap(message -> targetSession.send(Mono.just(targetSession.textMessage(message.getPayloadAsText()))))
                .doOnError(error -> log.error("Error forwarding client message: {}", error.getMessage()))
                .onErrorResume(error -> Mono.empty())
                .then();

            // Forward messages from target service to client
            Mono<Void> output = targetSession.receive()
                .doOnNext(message -> log.debug("Service -> Client: {}", message.getPayloadAsText()))
                .flatMap(message -> session.send(Mono.just(session.textMessage(message.getPayloadAsText()))))
                .doOnError(error -> log.error("Error forwarding service message: {}", error.getMessage()))
                .onErrorResume(error -> Mono.empty())
                .then();

            return Mono.zip(input, output).then();
        }).doOnError(error -> log.error("WebSocket proxy error: {}", error.getMessage()));
    }

    private String extractServiceName(String path) {
        // Path format: /ws-proxy/{serviceName}/...
        String[] parts = path.split("/");
        if (parts.length >= 3 && "ws-proxy".equals(parts[1])) {
            return parts[2];
        }
        return null;
    }

    private String extractTokenFromSession(WebSocketSession session) {
        // Try to get token from query parameters first
        String token = session.getHandshakeInfo().getUri().getQuery();
        if (token != null && token.contains("token=")) {
            String[] params = token.split("&");
            for (String param : params) {
                if (param.startsWith("token=")) {
                    return param.substring(6); // Remove "token="
                }
            }
        }

        // Try to get token from Authorization header
        HttpHeaders headers = session.getHandshakeInfo().getHeaders();
        String authHeader = headers.getFirst(HttpHeaders.AUTHORIZATION);
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7); // Remove "Bearer "
        }

        return null;
    }

    private String extractToken(Authentication authentication) {
        if (authentication instanceof WydenAuthenticationToken wydenAuth) {
            return wydenAuth.getCredentials().toString();
        }
        return null;
    }
}

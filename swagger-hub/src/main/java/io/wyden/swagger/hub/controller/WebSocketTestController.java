package io.wyden.swagger.hub.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/websocket")
public class WebSocketTestController {

    @GetMapping("/info/{serviceName}")
    public ResponseEntity<Map<String, Object>> getWebSocketInfo(@PathVariable String serviceName) {
        return ResponseEntity.ok(Map.of(
            "websocketUrl", "ws://localhost:8077/ws-proxy/" + serviceName + "/graphql/ws",
            "description", "WebSocket proxy endpoint for " + serviceName + " service",
            "authentication", "Add token as query parameter: ?token=YOUR_BEARER_TOKEN",
            "example", "ws://localhost:8077/ws-proxy/" + serviceName + "/graphql/ws?token=eyJhbGciOiJSUzI1NiIs...",
            "protocols", Map.of(
                "graphql-ws", "GraphQL WebSocket Protocol",
                "graphql-transport-ws", "GraphQL Transport WebSocket Protocol"
            )
        ));
    }

    @GetMapping("/test")
    public ResponseEntity<String> getTestPage() {
        String html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>WebSocket Proxy Test</title>
            </head>
            <body>
                <h1>WebSocket Proxy Test</h1>
                <div>
                    <label>Service:</label>
                    <select id="service">
                        <option value="settlement">Settlement</option>
                        <option value="rest-api-server">REST API Server</option>
                        <option value="risk-engine">Risk Engine</option>
                    </select>
                </div>
                <div>
                    <label>Token:</label>
                    <input type="text" id="token" placeholder="Bearer token" style="width: 400px;">
                </div>
                <div>
                    <button onclick="connect()">Connect</button>
                    <button onclick="disconnect()">Disconnect</button>
                </div>
                <div>
                    <label>Message:</label>
                    <input type="text" id="message" placeholder="JSON message">
                    <button onclick="sendMessage()">Send</button>
                </div>
                <div>
                    <h3>Messages:</h3>
                    <div id="messages" style="border: 1px solid #ccc; height: 300px; overflow-y: scroll; padding: 10px;"></div>
                </div>
                
                <script>
                    let ws = null;
                    
                    function connect() {
                        const service = document.getElementById('service').value;
                        const token = document.getElementById('token').value;
                        const url = `ws://localhost:8077/ws-proxy/${service}/graphql/ws?token=${token}`;
                        
                        ws = new WebSocket(url);
                        
                        ws.onopen = function() {
                            addMessage('Connected to ' + url);
                        };
                        
                        ws.onmessage = function(event) {
                            addMessage('Received: ' + event.data);
                        };
                        
                        ws.onclose = function() {
                            addMessage('Disconnected');
                        };
                        
                        ws.onerror = function(error) {
                            addMessage('Error: ' + error);
                        };
                    }
                    
                    function disconnect() {
                        if (ws) {
                            ws.close();
                        }
                    }
                    
                    function sendMessage() {
                        const message = document.getElementById('message').value;
                        if (ws && message) {
                            ws.send(message);
                            addMessage('Sent: ' + message);
                        }
                    }
                    
                    function addMessage(message) {
                        const messages = document.getElementById('messages');
                        const div = document.createElement('div');
                        div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
                        messages.appendChild(div);
                        messages.scrollTop = messages.scrollHeight;
                    }
                </script>
            </body>
            </html>
            """;
        
        return ResponseEntity.ok()
            .header("Content-Type", "text/html")
            .body(html);
    }
}

package io.wyden.swagger.hub.controller;

import io.wyden.swagger.hub.config.SwaggerServiceConfig;
import io.wyden.swagger.hub.model.ServiceInfo;
import io.wyden.swagger.hub.security.WydenAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Optional;

@RestController
@RequestMapping("/proxy")
public class ProxyController {

    private static final Logger log = LoggerFactory.getLogger(ProxyController.class);

    private final WebClient webClient;
    private final SwaggerServiceConfig config;

    public ProxyController(WebClient webClient, SwaggerServiceConfig config) {
        this.webClient = webClient;
        this.config = config;
    }

    @RequestMapping(value = "/{serviceName}/**", method = {
        RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, 
        RequestMethod.DELETE, RequestMethod.PATCH, RequestMethod.OPTIONS
    })
    public Mono<ResponseEntity<String>> proxyRequest(
            @PathVariable String serviceName,
            @RequestBody(required = false) String body,
            Authentication authentication,
            ServerHttpRequest request) {

        // Extract the path after /proxy/{serviceName}
        String requestPath = request.getURI().getPath();
        String targetPath = requestPath.replaceFirst("/proxy/" + serviceName, "");
        String queryString = request.getURI().getQuery();
        
        log.info("Proxying request: {} {} -> service: {}, path: {}", 
            request.getMethod(), requestPath, serviceName, targetPath);

        Optional<ServiceInfo> serviceOpt = config.getServices().stream()
            .filter(s -> s.getName().equals(serviceName))
            .findFirst();

        if (serviceOpt.isEmpty()) {
            log.warn("Service not found: {}", serviceName);
            return Mono.just(ResponseEntity.notFound().build());
        }

        ServiceInfo service = serviceOpt.get();
        String fullUrl = service.getUrl() + targetPath;
        if (queryString != null && !queryString.isEmpty()) {
            fullUrl += "?" + queryString;
        }

        log.info("Forwarding to: {} {}", request.getMethod(), fullUrl);

        // Create request based on HTTP method
        WebClient.RequestBodySpec requestSpec = webClient
            .method(request.getMethod())
            .uri(fullUrl);

        // Add Authorization header if available
        String token = extractToken(authentication);
        if (token != null && !token.isEmpty()) {
            requestSpec = requestSpec.header("Authorization", "Bearer " + token);
            log.debug("Added Authorization header for service: {}", serviceName);
        } else {
            log.warn("No token available for request to: {}", serviceName);
        }

        // Copy headers from original request (except Host and Authorization)
        final WebClient.RequestBodySpec finalRequestSpec = requestSpec;
        request.getHeaders().forEach((name, values) -> {
            if (!name.equalsIgnoreCase("host") &&
                !name.equalsIgnoreCase("authorization") &&
                !name.equalsIgnoreCase("content-length")) {
                finalRequestSpec.header(name, values.toArray(new String[0]));
            }
        });
        requestSpec = finalRequestSpec;

        // Add body for POST/PUT/PATCH requests
        Mono<ResponseEntity<String>> response;
        if (body != null && !body.isEmpty() && 
            (request.getMethod() == HttpMethod.POST || 
             request.getMethod() == HttpMethod.PUT || 
             request.getMethod() == HttpMethod.PATCH)) {
            response = requestSpec
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .toEntity(String.class);
        } else {
            response = requestSpec
                .retrieve()
                .toEntity(String.class);
        }

        String finalFullUrl = fullUrl;
        return response
            .doOnSuccess(resp -> log.info("Proxy response: {} for {}", resp.getStatusCode(), finalFullUrl))
            .doOnError(error -> log.error("Proxy error for {}: {}", finalFullUrl, error.getMessage()))
            .onErrorReturn(ResponseEntity.status(500).body("{\"error\":\"Proxy request failed\"}"));
    }

    private String extractToken(Authentication authentication) {
        if (authentication instanceof WydenAuthenticationToken jwtAuth) {
            return jwtAuth.getJwt().getTokenValue();
        }
        return null;
    }
}

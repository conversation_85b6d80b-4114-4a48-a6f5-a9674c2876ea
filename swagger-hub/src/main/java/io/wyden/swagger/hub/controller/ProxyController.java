package io.wyden.swagger.hub.controller;

import io.wyden.swagger.hub.config.SwaggerServiceConfig;
import io.wyden.swagger.hub.model.ServiceInfo;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Optional;

@RestController
@RequestMapping("/proxy")
public class ProxyController {

    private final WebClient webClient;
    private final SwaggerServiceConfig config;

    public ProxyController(WebClient webClient, SwaggerServiceConfig config) {
        this.webClient = webClient;
        this.config = config;
    }

    @RequestMapping(value = "/{serviceName}/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.PATCH})
    public Mono<ResponseEntity<String>> proxyRequest(
            @PathVariable String serviceName,
            @RequestBody(required = false) String body,
            Authentication authentication,
            ServerHttpRequest request) {

        // Extract the path after /proxy/{serviceName}
        String requestPath = request.getURI().getPath();
        String targetPath = requestPath.replaceFirst("/proxy/" + serviceName, "");

        Optional<ServiceInfo> serviceOpt = config.getServices().stream()
            .filter(s -> s.getName().equals(serviceName))
            .findFirst();

        if (serviceOpt.isEmpty()) {
            return Mono.just(ResponseEntity.notFound().build());
        }

        ServiceInfo service = serviceOpt.get();
        String fullUrl = service.getUrl() + targetPath;

        var request = webClient.get().uri(fullUrl);

        // Add Authorization header if available
        String token = extractToken(authentication);
        if (token != null && !token.isEmpty()) {
            request = request.header("Authorization", "Bearer " + token);
        }

        return request
            .retrieve()
            .toEntity(String.class)
            .onErrorReturn(ResponseEntity.status(500).body("Error calling service"));
    }

    private String extractToken(Authentication authentication) {
        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            return jwtAuth.getToken().getTokenValue();
        }
        return null;
    }
}

package io.wyden.swagger.hub.config;

import io.wyden.swagger.hub.model.ServiceInfo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "swagger.aggregator")
public class SwaggerServiceConfig {

    private List<ServiceInfo> services = new ArrayList<>();

    public List<ServiceInfo> getServices() {
        return services;
    }

    public void setServices(List<ServiceInfo> services) {
        this.services = services;
    }
}

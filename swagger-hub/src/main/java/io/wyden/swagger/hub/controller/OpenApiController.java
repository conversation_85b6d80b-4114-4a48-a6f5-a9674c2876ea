package io.wyden.swagger.hub.controller;

import io.wyden.swagger.hub.service.SwaggerAggregatorService;
import io.wyden.swagger.hub.service.SwaggerProxyService;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Map;

@RestController
public class OpenApiController {

    private final SwaggerAggregatorService swaggerAggregatorService;
    private final SwaggerProxyService swaggerProxyService;

    public OpenApiController(SwaggerAggregatorService swaggerAggregatorService, SwaggerProxyService swaggerProxyService) {
        this.swaggerAggregatorService = swaggerAggregatorService;
        this.swaggerProxyService = swaggerProxyService;
    }

    @GetMapping(value = "/v3/api-docs/aggregated", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<Map<String, Object>> getAggregatedOpenApiDocs(Authentication authentication) {
        String token = extractToken(authentication);
        return swaggerAggregatorService.getAggregatedSwagger(token);
    }

    @GetMapping(value = "/v3/api-docs/{serviceName}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<Map<String, Object>> getServiceOpenApiDocs(@PathVariable String serviceName, Authentication authentication) {
        String token = extractToken(authentication);
        return swaggerProxyService.getServiceSwagger(serviceName, token);
    }

    private String extractToken(Authentication authentication) {
        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            return jwtAuth.getToken().getTokenValue();
        }
        return null; // Return null if no authentication - service will handle this
    }
}

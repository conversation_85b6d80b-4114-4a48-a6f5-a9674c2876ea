package io.wyden.swagger.hub.controller;

import io.wyden.swagger.hub.service.SwaggerAggregatorService;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Map;

@RestController
public class OpenApiController {

    private final SwaggerAggregatorService swaggerAggregatorService;

    public OpenApiController(SwaggerAggregatorService swaggerAggregatorService) {
        this.swaggerAggregatorService = swaggerAggregatorService;
    }

    @GetMapping(value = "/v3/api-docs", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<Map<String, Object>> getOpenApiDocs(Authentication authentication) {
        String token = extractToken(authentication);
        return swaggerAggregatorService.getAggregatedSwagger(token);
    }

    private String extractToken(Authentication authentication) {
        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            return jwtAuth.getToken().getTokenValue();
        }
        throw new IllegalArgumentException("Invalid authentication type");
    }
}

package io.wyden.swagger.hub.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.wyden.swagger.hub.config.SwaggerServiceConfig;
import io.wyden.swagger.hub.exception.SwaggerFetchException;
import io.wyden.swagger.hub.exception.SwaggerParseException;
import io.wyden.swagger.hub.model.ServiceInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class SwaggerProxyService {

    private static final Logger log = LoggerFactory.getLogger(SwaggerProxyService.class);

    private final WebClient webClient;
    private final SwaggerServiceConfig config;
    private final ObjectMapper objectMapper;

    public SwaggerProxyService(WebClient webClient, SwaggerServiceConfig config) {
        this.webClient = webClient;
        this.config = config;
        this.objectMapper = new ObjectMapper();
    }

    public Mono<Map<String, Object>> getServiceSwagger(String serviceName, String token) {
        log.info("Fetching Swagger documentation for service: {}", serviceName);

        Optional<ServiceInfo> serviceOpt = config.getServices().stream()
            .filter(s -> s.getName().equals(serviceName))
            .filter(ServiceInfo::isEnabled)
            .findFirst();

        if (serviceOpt.isEmpty()) {
            log.warn("Service not found or disabled: {}", serviceName);
            return Mono.just(createNotFoundResponse(serviceName));
        }

        ServiceInfo service = serviceOpt.get();
        return fetchAndModifySwaggerDoc(token, service);
    }

    private Mono<Map<String, Object>> fetchAndModifySwaggerDoc(String token, ServiceInfo service) {
        String fullUrl = service.getUrl() + service.getSwaggerPath();

        var webRequest = webClient.get().uri(fullUrl);

        // Add Authorization header only if token is available
        if (token != null && !token.isEmpty()) {
            webRequest = webRequest.header("Authorization", "Bearer " + token);
        }

        return webRequest
            .retrieve()
            .onStatus(HttpStatusCode::isError, response -> {
                log.warn("Error fetching swagger from {}: {}", service.getName(), response.statusCode());
                return Mono.error(new SwaggerFetchException("Failed to fetch from " + service.getName()));
            })
            .bodyToMono(String.class)
            .map(swaggerJson -> {
                try {
                    Map<String, Object> swaggerDoc = objectMapper.readValue(swaggerJson, Map.class);
                    return modifySwaggerForDirectAccess(swaggerDoc, service);
                } catch (Exception e) {
                    throw new SwaggerParseException("Failed to parse swagger from " + service.getName(), e);
                }
            })
            .timeout(Duration.ofSeconds(10))
            .onErrorReturn(createErrorResponse(service.getName()));
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> modifySwaggerForDirectAccess(Map<String, Object> swagger, ServiceInfo service) {
        Map<String, Object> modified = new LinkedHashMap<>(swagger);

        // Update servers to point directly to the service
        modified.put("servers", List.of(
            Map.of("url", service.getUrl(), "description", service.getName() + " service")
        ));

        // Add Keycloak security if not present
        if (modified.containsKey("components")) {
            Map<String, Object> components = (Map<String, Object>) modified.get("components");
            if (components.containsKey("securitySchemes")) {
                Map<String, Object> securitySchemes = (Map<String, Object>) components.get("securitySchemes");
                securitySchemes.put("keycloak", createKeycloakSecurity());
            } else {
                components.put("securitySchemes", Map.of("keycloak", createKeycloakSecurity()));
            }
        } else {
            modified.put("components", Map.of(
                "securitySchemes", Map.of("keycloak", createKeycloakSecurity())
            ));
        }

        // Add global security requirement to all operations
        modified.put("security", List.of(Map.of("keycloak", List.of())));

        // Add security requirement to each path operation to ensure 401 responses
        if (modified.containsKey("paths")) {
            Map<String, Object> paths = (Map<String, Object>) modified.get("paths");
            addSecurityToAllOperations(paths);
        }

        return modified;
    }

    @SuppressWarnings("unchecked")
    private void addSecurityToAllOperations(Map<String, Object> paths) {
        for (Map.Entry<String, Object> pathEntry : paths.entrySet()) {
            if (pathEntry.getValue() instanceof Map) {
                Map<String, Object> pathItem = (Map<String, Object>) pathEntry.getValue();

                // Add security and 401 response to each HTTP method
                for (String method : List.of("get", "post", "put", "delete", "patch", "options", "head", "trace")) {
                    if (pathItem.containsKey(method) && pathItem.get(method) instanceof Map) {
                        Map<String, Object> operation = new LinkedHashMap<>((Map<String, Object>) pathItem.get(method));

                        // Add security requirement
                        operation.put("security", List.of(Map.of("keycloak", List.of())));

                        // Add 401 response
                        if (operation.containsKey("responses")) {
                            Map<String, Object> responses = new LinkedHashMap<>((Map<String, Object>) operation.get("responses"));
                            responses.put("401", Map.of(
                                "description", "Unauthorized - Bearer token required",
                                "content", Map.of(
                                    "application/json", Map.of(
                                        "schema", Map.of(
                                            "type", "object",
                                            "properties", Map.of(
                                                "error", Map.of("type", "string", "example", "Unauthorized"),
                                                "message", Map.of("type", "string", "example", "Bearer token required")
                                            )
                                        )
                                    )
                                )
                            ));
                            operation.put("responses", responses);
                        } else {
                            operation.put("responses", Map.of(
                                "401", Map.of(
                                    "description", "Unauthorized - Bearer token required"
                                )
                            ));
                        }

                        pathItem.put(method, operation);
                    }
                }
            }
        }
    }

    private Map<String, Object> createKeycloakSecurity() {
        return Map.of(
            "type", "http",
            "scheme", "bearer",
            "bearerFormat", "JWT",
            "description", "Keycloak JWT token"
        );
    }

    private Map<String, Object> createNotFoundResponse(String serviceName) {
        return Map.of(
            "openapi", "3.0.3",
            "info", Map.of(
                "title", "Service Not Found",
                "description", "Service '" + serviceName + "' not found or disabled",
                "version", "1.0.0"
            ),
            "paths", Map.of()
        );
    }

    private Map<String, Object> createErrorResponse(String serviceName) {
        return Map.of(
            "openapi", "3.0.3",
            "info", Map.of(
                "title", "Service Error",
                "description", "Failed to fetch documentation for service '" + serviceName + "'",
                "version", "1.0.0"
            ),
            "paths", Map.of()
        );
    }
}

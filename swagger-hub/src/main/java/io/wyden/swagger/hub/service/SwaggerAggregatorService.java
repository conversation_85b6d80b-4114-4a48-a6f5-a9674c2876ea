package io.wyden.swagger.hub.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import io.wyden.swagger.hub.config.SwaggerServiceConfig;
import io.wyden.swagger.hub.exception.SwaggerFetchException;
import io.wyden.swagger.hub.exception.SwaggerParseException;
import io.wyden.swagger.hub.model.ServiceInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SwaggerAggregatorService {

    private static final Logger log = LoggerFactory.getLogger(SwaggerAggregatorService.class);

    private final WebClient webClient;
    private final SwaggerServiceConfig config;
    private final ObjectMapper objectMapper;
    private final ObjectMapper yamlMapper;

    public SwaggerAggregatorService(WebClient webClient, SwaggerServiceConfig config) {
        this.webClient = webClient;
        this.config = config;
        this.objectMapper = new ObjectMapper();
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
    }
    
    public Mono<Map<String, Object>> getAggregatedSwagger(String token) {
        log.info("Aggregating Swagger documentation from {} services", config.getServices().size());
        
        List<Mono<ServiceSwaggerData>> swaggerCalls = config.getServices().stream()
            .filter(ServiceInfo::isEnabled)
            .map(service -> fetchSwaggerDoc(token, service))
            .collect(Collectors.toList());
        
        return Flux.fromIterable(swaggerCalls)
            .flatMap(mono -> mono.onErrorResume(error -> {
                log.warn("Failed to fetch swagger from service", error);
                return Mono.empty();
            }))
            .collectList()
            .map(this::mergeSwaggerDocuments)
            .doOnSuccess(result -> log.info("Successfully aggregated Swagger documentation"))
            .doOnError(error -> log.error("Failed to aggregate Swagger documentation", error));
    }
    
    private Mono<ServiceSwaggerData> fetchSwaggerDoc(String token, ServiceInfo service) {
        String fullUrl = service.getUrl() + service.getSwaggerPath();
        
        return webClient.get()
            .uri(fullUrl)
            .header("Authorization", "Bearer " + token)
            .retrieve()
            .onStatus(HttpStatusCode::isError, response -> {
                log.warn("Error fetching swagger from {}: {}", service.getName(), response.statusCode());
                return Mono.error(new SwaggerFetchException("Failed to fetch from " + service.getName()));
            })
            .bodyToMono(String.class)
            .map(swaggerJson -> {
                try {
                    Map<String, Object> swaggerDoc = objectMapper.readValue(swaggerJson, Map.class);
                    return new ServiceSwaggerData(service, swaggerDoc);
                } catch (Exception e) {
                    throw new SwaggerParseException("Failed to parse swagger from " + service.getName(), e);
                }
            })
            .timeout(Duration.ofSeconds(10));
    }
    
    @SuppressWarnings("unchecked")
    private Map<String, Object> mergeSwaggerDocuments(List<ServiceSwaggerData> swaggerDocs) {
        Map<String, Object> aggregated = new LinkedHashMap<>();
        
        // Base OpenAPI structure
        aggregated.put("openapi", "3.0.3");
        aggregated.put("info", createAggregatedInfo());
        aggregated.put("servers", createServers());
        
        Map<String, Object> paths = new LinkedHashMap<>();
        Map<String, Object> components = new LinkedHashMap<>();
        Map<String, Object> schemas = new LinkedHashMap<>();
        Map<String, Object> securitySchemes = new LinkedHashMap<>();
        List<Map<String, Object>> tags = new ArrayList<>();
        
        for (ServiceSwaggerData serviceData : swaggerDocs) {
            ServiceInfo service = serviceData.getService();
            Map<String, Object> swagger = serviceData.getSwaggerDoc();
            
            // Merge paths with service prefix
            if (swagger.containsKey("paths")) {
                Map<String, Object> servicePaths = (Map<String, Object>) swagger.get("paths");
                String pathPrefix = service.getBasePath() != null ? service.getBasePath() : "/" + service.getName();
                
                servicePaths.forEach((path, pathItem) -> {
                    String prefixedPath = pathPrefix + path;
                    paths.put(prefixedPath, pathItem);
                });
            }
            
            // Merge components/schemas with service prefix
            if (swagger.containsKey("components")) {
                Map<String, Object> serviceComponents = (Map<String, Object>) swagger.get("components");
                
                if (serviceComponents.containsKey("schemas")) {
                    Map<String, Object> serviceSchemas = (Map<String, Object>) serviceComponents.get("schemas");
                    serviceSchemas.forEach((schemaName, schema) -> {
                        String prefixedName = service.getName() + "_" + schemaName;
                        schemas.put(prefixedName, schema);
                    });
                }
                
                if (serviceComponents.containsKey("securitySchemes")) {
                    Map<String, Object> serviceSecuritySchemes = (Map<String, Object>) serviceComponents.get("securitySchemes");
                    securitySchemes.putAll(serviceSecuritySchemes);
                }
            }
            
            // Merge tags
            if (swagger.containsKey("tags")) {
                List<Map<String, Object>> serviceTags = (List<Map<String, Object>>) swagger.get("tags");
                serviceTags.forEach(tag -> {
                    Map<String, Object> modifiedTag = new LinkedHashMap<>(tag);
                    modifiedTag.put("name", service.getName() + "_" + tag.get("name"));
                    if (tag.containsKey("description")) {
                        modifiedTag.put("description", "[" + service.getName() + "] " + tag.get("description"));
                    }
                    tags.add(modifiedTag);
                });
            }
        }
        
        // Add Keycloak security
        securitySchemes.put("keycloak", createKeycloakSecurity());
        
        components.put("schemas", schemas);
        components.put("securitySchemes", securitySchemes);
        
        aggregated.put("paths", paths);
        aggregated.put("components", components);
        aggregated.put("tags", tags);
        aggregated.put("security", List.of(Map.of("keycloak", List.of())));
        
        return aggregated;
    }
    
    private Map<String, Object> createAggregatedInfo() {
        Map<String, Object> info = new LinkedHashMap<>();
        info.put("title", "Aggregated API Documentation");
        info.put("description", "Combined API documentation from multiple microservices");
        info.put("version", "1.0.0");
        info.put("contact", Map.of(
            "name", "API Team",
            "email", "<EMAIL>"
        ));
        return info;
    }
    
    private List<Map<String, Object>> createServers() {
        return List.of(
            Map.of("url", "http://localhost:8080", "description", "Local development"),
            Map.of("url", "https://api.company.com", "description", "Production")
        );
    }
    
    private Map<String, Object> createKeycloakSecurity() {
        return Map.of(
            "type", "http",
            "scheme", "bearer",
            "bearerFormat", "JWT",
            "description", "Keycloak JWT token"
        );
    }
    
    private static class ServiceSwaggerData {
        private final ServiceInfo service;
        private final Map<String, Object> swaggerDoc;

        public ServiceSwaggerData(ServiceInfo service, Map<String, Object> swaggerDoc) {
            this.service = service;
            this.swaggerDoc = swaggerDoc;
        }

        public ServiceInfo getService() {
            return service;
        }

        public Map<String, Object> getSwaggerDoc() {
            return swaggerDoc;
        }
    }
}
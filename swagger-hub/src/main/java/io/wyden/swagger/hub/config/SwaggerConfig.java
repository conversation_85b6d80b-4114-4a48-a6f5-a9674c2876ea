package io.wyden.swagger.hub.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Configuration
public class SwaggerConfig {
    private static final String OPEN_ID_SCHEME_NAME = "keycloak";

    @Value("${openapi.server.url:}")
    String serverUrl;

    @Value("${spring.security.oauth2.client.provider.keycloak.issuer-uri}")
    String issuerUri;

    @Value("${keycloak.realm}")
    String realmName;

    @Value("${swagger.keycloak.host}")
    String swaggerKeycloakHost;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("Aggregated API Documentation")
                .description("Combined API documentation from multiple microservices")
                .version("1.0.0"))
            .components(new Components()
                .addSecuritySchemes(OPEN_ID_SCHEME_NAME, createOAuthScheme()))
            .addSecurityItem(new SecurityRequirement().addList(OPEN_ID_SCHEME_NAME))
            .servers(getServers());
    }

    private SecurityScheme createOAuthScheme() {
        return new SecurityScheme()
            .type(SecurityScheme.Type.OAUTH2)
            .flows(new io.swagger.v3.oas.models.security.OAuthFlows()
                .authorizationCode(new io.swagger.v3.oas.models.security.OAuthFlow()
                    .authorizationUrl(swaggerKeycloakHost + "/realms/" + realmName + "/protocol/openid-connect/auth")
                    .tokenUrl(swaggerKeycloakHost + "/realms/" + realmName + "/protocol/openid-connect/token")
                    .scopes(new io.swagger.v3.oas.models.security.Scopes()
                        .addString("openid", "OpenID Connect scope"))));
    }

    private List<Server> getServers() {
        if (isNotBlank(serverUrl)) {
            return List.of(new Server().url(serverUrl).description("Custom server"));
        }
        return List.of(
            new Server().url("http://localhost:8077").description("Local development"),
            new Server().url("https://api.company.com").description("Production")
        );
    }

    @Bean
    public GroupedOpenApi aggregatedApi() {
        return GroupedOpenApi.builder()
            .group("aggregated")
            .pathsToMatch("/v3/api-docs/aggregated")
            .build();
    }
}

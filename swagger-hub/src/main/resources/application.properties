spring.application.name=swagger-hub

server.port=8077

settlement.host = http://localhost:8067
keycloak.host=http://localhost:8080
access.gateway.host = http://localhost:8089
reference.data.host = http://localhost:8098
booking.engine.host=http://localhost:8100
target.registry.host=http://localhost:8066
risk.engine.host = http://localhost:8300
audit.server.host=http://localhost:8030
order.history.host=http://localhost:8040
clob.gateway.host=http://localhost:8069
quoting.engine.host=http://localhost:8071
aeron.archive.host=http://localhost:8072
broker.config.service.host=http://localhost:8049
rate.service.host=http://localhost:8052
rest.api.service.host=http://localhost:8095

spring.security.oauth2.client.registration.keycloak.client-id=access-gateway-app
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.keycloak.scope=openid
spring.security.oauth2.client.provider.keycloak.issuer-uri=${keycloak.host}/realms/${keycloak.realm}
spring.security.oauth2.resourceserver.jwt.issuer-uri=${keycloak.host}/realms/${keycloak.realm}
spring.security.oauth2.client.provider.keycloak.user-name-attribute=preferred_username
security.jwt.issuer.validation.disabled=true
keycloak.realm=Wyden
swagger.keycloak.host=${keycloak.host}

# Configure SpringDoc to use our custom aggregated docs
springdoc.swagger-ui.url=/v3/api-docs/aggregated
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.oauth.client-id=access-gateway-app
springdoc.swagger-ui.oauth.realm=${keycloak.realm}
springdoc.swagger-ui.oauth.app-name=swagger-hub
springdoc.swagger-ui.oauth.scopes=openid

# Swagger aggregator configuration
swagger.aggregator.services[0].name=settlement
swagger.aggregator.services[0].url=${settlement.host}
swagger.aggregator.services[0].swagger-path=/v3/api-docs
swagger.aggregator.services[0].base-path=/settlement
swagger.aggregator.services[0].enabled=true

swagger.aggregator.services[1].name=access-gateway
swagger.aggregator.services[1].url=${access.gateway.host}
swagger.aggregator.services[1].swagger-path=/v3/api-docs
swagger.aggregator.services[1].base-path=/access-gateway
swagger.aggregator.services[1].enabled=true

swagger.aggregator.services[2].name=reference-data
swagger.aggregator.services[2].url=${reference.data.host}
swagger.aggregator.services[2].swagger-path=/v3/api-docs
swagger.aggregator.services[2].base-path=/reference-data
swagger.aggregator.services[2].enabled=true

swagger.aggregator.services[3].name=booking-engine
swagger.aggregator.services[3].url=${booking.engine.host}
swagger.aggregator.services[3].swagger-path=/v3/api-docs
swagger.aggregator.services[3].base-path=/booking-engine
swagger.aggregator.services[3].enabled=true

swagger.aggregator.services[4].name=target-registry
swagger.aggregator.services[4].url=${target.registry.host}
swagger.aggregator.services[4].swagger-path=/v3/api-docs
swagger.aggregator.services[4].base-path=/target-registry
swagger.aggregator.services[4].enabled=true

swagger.aggregator.services[5].name=risk-engine
swagger.aggregator.services[5].url=${risk.engine.host}
swagger.aggregator.services[5].swagger-path=/v3/api-docs
swagger.aggregator.services[5].base-path=/risk-engine
swagger.aggregator.services[5].enabled=true

swagger.aggregator.services[6].name=audit-server
swagger.aggregator.services[6].url=${audit.server.host}
swagger.aggregator.services[6].swagger-path=/v3/api-docs
swagger.aggregator.services[6].base-path=/audit-server
swagger.aggregator.services[6].enabled=true

swagger.aggregator.services[7].name=order-history
swagger.aggregator.services[7].url=${order.history.host}
swagger.aggregator.services[7].swagger-path=/v3/api-docs
swagger.aggregator.services[7].base-path=/order-history
swagger.aggregator.services[7].enabled=true

swagger.aggregator.services[8].name=clob-gateway
swagger.aggregator.services[8].url=${clob.gateway.host}
swagger.aggregator.services[8].swagger-path=/v3/api-docs
swagger.aggregator.services[8].base-path=/clob-gateway
swagger.aggregator.services[8].enabled=true

swagger.aggregator.services[9].name=quoting-engine
swagger.aggregator.services[9].url=${quoting.engine.host}
swagger.aggregator.services[9].swagger-path=/v3/api-docs
swagger.aggregator.services[9].base-path=/quoting-engine
swagger.aggregator.services[9].enabled=true

swagger.aggregator.services[10].name=aeron-archive
swagger.aggregator.services[10].url=${aeron.archive.host}
swagger.aggregator.services[10].swagger-path=/v3/api-docs
swagger.aggregator.services[10].base-path=/aeron-archive
swagger.aggregator.services[10].enabled=true

swagger.aggregator.services[11].name=broker-config-service
swagger.aggregator.services[11].url=${broker.config.service.host}
swagger.aggregator.services[11].swagger-path=/v3/api-docs
swagger.aggregator.services[11].base-path=/broker-config-service
swagger.aggregator.services[11].enabled=true

swagger.aggregator.services[12].name=rate-service
swagger.aggregator.services[12].url=${rate.service.host}
swagger.aggregator.services[12].swagger-path=/v3/api-docs
swagger.aggregator.services[12].base-path=/rate-service
swagger.aggregator.services[12].enabled=true

swagger.aggregator.services[13].name=rest-api-server
swagger.aggregator.services[13].url=${rest.api.service.host}
swagger.aggregator.services[13].swagger-path=/v3/api-docs
swagger.aggregator.services[13].base-path=/rest-api
swagger.aggregator.services[13].enabled=true
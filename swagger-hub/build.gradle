plugins {
    id 'java'
    id 'idea'
    id 'version-catalog'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.jacocoToCobertura
}

group = 'io.wyden'
version = '1.0-SNAPSHOT'

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

dependencies {
    implementation dependencyCatalog.springdoc.openapi.starter.webflux.ui
    implementation dependencyCatalog.springdoc.openapi.starter.webmvc.ui

    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.oauth2.resource.server
    implementation dependencyCatalog.spring.boot.starter.oauth2.client
    implementation dependencyCatalog.spring.boot.starter.actuator

    implementation dependencyCatalog.jackson.dataformat.yaml
    implementation dependencyCatalog.spring.boot.starter.validation

    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64'} }

    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.spring.security.test
    testImplementation dependencyCatalog.reactor.test

}

test {
    useJUnitPlatform()
}
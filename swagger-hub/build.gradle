plugins {
    id 'java'
    id 'version-catalog'
    id 'maven-publish'
    alias dependencyCatalog.plugins.sonarqube
}

group = 'io.wyden'
version = '1.0-SNAPSHOT'

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

dependencies {
    implementation dependencyCatalog.springdoc.openapi.starter.webflux.ui

    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'

}

test {
    useJUnitPlatform()
}
# NG Grooming Process

> **Owner**:\
> CTO

> **Audience**:\
> Team Leads, Developers, QA, UI, DevOps

> **Process Inputs**:\
> Non-groomed Jira tickets

> **Process Outputs**:\
> Estimated, well-described Jira tickets

# Table of Contents
[[_TOC_]]

## Purpose
Grooming sessions are conducted by **Team Leaders**. TLs can decide to have a joint, cross-team grooming session if required.\
The NG Grooming Process prepares tasks (features, bugs, improvements) for sprints by making them clear, estimated, and understood by all teams (UI, Developers, QA, etc.). It helps the Sprint Committee plan sprints, avoids knowledge silos, ensures accurate hour estimates, and involves stakeholders early to align on goals and scope.

## Steps
```mermaid
---
title: NG Grooming Process
---
flowchart TD
  1["Ticket created (New)"] 
  subgraph Optional
  2["Additional Analysis (Specification in Progress)"]
  end
  4[Grooming]
  5["Groomed (Ready For Dev)"]
  1 --> 4
  4 --> 5
  4 <--> 2
  4 --> |Ticket Rejection|2
  1 --> |Ticket Rejection|2

```

### Ticket created (New)
Epics, stories, bugs, or improvements are identified & created by stakeholders (Project Managers, Product Manager, Developers, QA). For epics and stories, a spec document with listed user stories is required.
For smaller bugs and improvements, a good description is enough.

#### Submission for grooming
Stakeholders submit tasks for grooming:

Small Tasks: Create a JIRA ticket at least 1 week before the Sprint Committee meeting (Thursday of stabilization week).
Large Features (Epics): Submit a User Story document at least one sprint ahead for analysis and breakdown.
Provide a prioritized task list (via email or Teams) one week before the Sprint Committee meeting.

### Grooming session

```mermaid
flowchart TD
    1[Next Release Tickets] --> 2[Stakeholders' Wishlists]
    2 --> 3[Backlog]

```

1. If the current Sprint is e.g. 2025.1, tickets assigned with fix version 2025.2 are groomed first, beginning from the Highest to Lowest Priority.
2. If tickets from #1 are groomed, then tickets from Stakeholders' wishlist are picked up. This can include long-term planned Epics.
3. Lastly, teams groom tickets from Roadmap, Tech, and PS Backlogs.

#### For Epics:
- Per the NG New Feature Process, Epics must undergo initial analysis and Guild of Architects (GoA) review, then be split into subtasks and divided among teams. If one breaks an epic into smaller tasks, then you can groom the initial ones (and have them Ready for Dev) while analyzing the next ones (Specification in Progress). 

#### For Smaller Features:
- Improvements and bugs can be entered directly into JIRA without separate User Story specifications.

#### Ballpark Estimation
For urgent stakeholder requests (e.g., client projects), a ballpark estimation may be provided:

- Recorded in a custom JIRA field: Ballpark Estimate (Hours).
- Clearly marked as non-binding and separate from the final estimate.
- Used for high-level planning but not sprint commitments.


### Groomed (Ready for Dev)
Groomed tickets move to Ready for Dev status in JIRA:

- Estimated, assigned to an Epic, and linked to a BO.
- Submitted to the Sprint Committee (Thursday of stabilization week) with team capacity (hours) provided by Tech Leads.

### Ticket Rejection
Tickets that:
- don't contain enough information
- don't have clear documentation
- are missing important assumptions

Can be rejected from grooming. The grooming master should put a relevant comment into the ticket, marking the stakeholders.
Before ticket rejection, TLs can pull in respective stakeholders to the grooming session.

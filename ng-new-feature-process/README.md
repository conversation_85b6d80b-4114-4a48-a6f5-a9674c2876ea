# NG New Feature Process
> **Owner**:\
> Product Manager

> **Audience**:\
> <PERSON><PERSON>, Product Manager, Project Manager, GoA Team, Team Leads, Developers, QA, UI, DevOps

> **Process Inputs**:\
> Specification

> **Process Outputs**:\
> Jira Epic

# Table of Contents
[[_TOC_]]

## Process Diagram

```mermaid
---
title: NG New Feature Process
---
flowchart TD
    subgraph Initial spec

    1[Spec draft]
    1b[Initial designs]
    end
    1c["Create Feature Ticket OR Epic "]
    subgraph Inception
    2[GOA review]
    3[UI Designs]
    3a[Test Scenarios Preparation]
    4@{ shape: diamond, label: "Signoffs in JIRA"}
    end
    5[For epics: create subtickets]
    subgraph JIRA flow
    6[< JIRA process >]
    end
    7@{ shape: diamond, label: "BO acceptance (IN BO)"}
    1 --> 1c
    
    1c --> 2
    
    1c --> 3a

    2 --> 4
    
    3a --> 4
    
    4 --> 5
    5 --> 6
    6 --> 7
```

## Specification

The feature request may have several sources, such as:
- Product Manager
- Project Manager
- CTO
- Architectural Team


> If feature is requested by Product Manager, Product Manager Sign-Off can be omitted

> If feature is requested by Architectural Team, Architectural Sign-Off and Product Manager Sign-Off can be omitted however, CTO sign-off must be acquired.

1. The specification is provided to Architectural Team Leader (if requested by Product Manager or CTO) or to Product Manager (if requested by Project Manager).
2. Both Product Manager and Architecture Team Leaders can circle back the specification to the author, if specification doesn't fit Product or Architecture constraints. Respective team should provide suggestions to the author.
3. Specification should be stored in [Central Specification Repository](../ng-spec-repository) for the Product
4. User Stories and Acceptance Criteria should be stored in [User Stories Repository](https://algotrader.atlassian.net/wiki/x/BoDWIQ)

The specification MUST contain:

- [x] Functional Description and Requirements
- [x] UI Wireframes (if applicable)
- [x] Computation tables (Excel, if applicable)
- [x] User Stories
- [x] Acceptance Criteria
- [x] Sign-off table (Product/Architecture)

## Product Sign-Off

The Product Manager evaluates the specification in terms of its alignment with the product development plan, potential conflicts with other features, and consistency with the overall product.
Upon positive approval of the specification, the Product Manager forwards it to the Architectural Team Leader for review.
The Product Manager may, in consultation with the author, modify the specification and/or return it to the author for incorporating comments and revisions.

## Architectural Sign-Off

The Guild of Architects evaluates the specification in terms of its alignment with the platform architecture and required performance.
The GoA is also responsible for preparing technical guidelines for the specification prior to the epic kickoff.
These guidelines should be included in the Epic Ticket. The GoA may return the specification to the author with suggestions regarding technical, performance or functional requirements.
Upon positive approval of the specification, the GoA creates an Epic in Jira, including the original specification and technical guidelines.

## Jira Epic

Once epic is created, the process is finished and continued with:

- [Grooming Process](../ng-grooming-process)
- [Mid-Term Planning Process](../ng-mid-term-planning-process)
- [Epic Kick-off Process](../ng-epic-kick-off-process)

Jira Epic must stick to the guidelines described here: [Jira Guidelines](../ng-jira-guidelines)

## Responsibilities

### Product Manager

- If acts as a specification author, responsible for creating a specification and delivering it to Architectural Team Leader
- If specification is created by other stakeholder, Product Manager is responsible for providing his Sign-off to the specification
- Saves the specification in [Specification Repository](../ng-spec-repository)
- Saves User Stories and Acceptance Criteria in [User Stories Repository](https://algotrader.atlassian.net/wiki/x/BoDWIQ)
- Signs-off Specification in Product-Sign Off section

> Product Manager can delegate his responsibilities to any direct reporter or any team member. However, Product Manager must provide his sign-off if required

### Project Manager

- Provides the specification to Product Manager
- Communicates with the client and specifies client-side requirements
- Communicates/negotiates with the client any push backs from Product and Arch
- Applies changes to specification according to Product and Arch guidelines

### CTO

- If acts as a specification author, responsible for creating a specification and delivering it to Architectural Team Leader
- Can act as an Architectural Team Leader
- Can give an Architectural Sign-off
- Can withdraw Architectural Sign-off given by Architectural Team Leader
- If acts as a specification author, saves specification in [Specification Repository](../ng-spec-repository)
- If acts as a specification author, saves User Stories and Acceptance Criteria in [User Stories Repository](https://algotrader.atlassian.net/wiki/x/BoDWIQ)

> CTO can delegate his responsibilities to any direct reporter or any team member. However, CTO must provide his sign-off if required

### Guild of Architects

- If acts as a specification author, responsible for creating a specification and getting CTO sign-off
- If acts as a specification author, saves specification in [Specification Repository](../ng-spec-repository)
- If acts as a specification author, saves User Stories and Acceptance Criteria in [User Stories Repository](https://algotrader.atlassian.net/wiki/x/BoDWIQ)
- Reviews Specification from technical point of view
- Provides suggestions to specification, related to technology, performance, infrastructure, functionality
- Prepares technical guidelines document
- Signs-off Specification in Architecture-Sign Off section
- Creates initial Jira Epic and finishes the process

> GoA can delegate its responsibilities to any team member. However, GoA must provide his sign-off if required

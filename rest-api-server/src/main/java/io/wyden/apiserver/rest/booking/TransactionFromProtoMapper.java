package io.wyden.apiserver.rest.booking;

import io.wyden.apiserver.rest.referencedata.instruments.mapper.InstrumentReferenceDataMapper;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.venueaccount.VenueAccountService;
import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.ClientAssetTradeSnapshot;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.DepositSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeSnapshot;
import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.SettlementSnapshot;
import io.wyden.published.booking.StreetAssetTradeSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WithdrawalSnapshot;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collection;

import static io.wyden.apiserver.rest.booking.BookingMessageToModelMapper.isoUtcTimeToLong;
import static io.wyden.apiserver.rest.utils.PortfolioUtils.getPortfolioName;
import static io.wyden.cloudutils.rabbitmq.MessageTranslator.toBigDecimalOrNull;
import static org.slf4j.LoggerFactory.getLogger;

@Component
public class TransactionFromProtoMapper {

    private static final Logger LOGGER = getLogger(TransactionFromProtoMapper.class);

    private final InstrumentReferenceDataMapper instrumentReferenceDataMapper;
    private final PortfolioRepository portfolioRepository;
    private final VenueAccountService venueAccountService;

    public TransactionFromProtoMapper(InstrumentReferenceDataMapper instrumentReferenceDataMapper,
                                      PortfolioRepository portfolioRepository,
                                      VenueAccountService venueAccountService) {
        this.instrumentReferenceDataMapper = instrumentReferenceDataMapper;
        this.portfolioRepository = portfolioRepository;
        this.venueAccountService = venueAccountService;
    }

    @Nullable
    public BookingModel.TransactionResponse getTransactionResponse(TransactionSnapshot transaction) {
        if (transaction.hasClientCashTrade()) {
            ClientCashTradeSnapshot trade = transaction.getClientCashTrade();
            String feeCurrency = mapFeeCurrency(trade.getTransactionFeeList());
            BigDecimal fee = mapFee(trade.getTransactionFeeList());

            Long settledDateTime = toOptionalDateTime(trade.getSettledDateTime());

            return new BookingModel.ClientCashTrade(
                trade.getUuid(),
                isoUtcTimeToLong(trade.getMetadata().getUpdatedAt()),
                trade.getDateTime(),
                trade.getExecutionId(),
                trade.getVenueExecutionId(),
                fee,
                feeCurrency,
                trade.getDescription(),
                toBigDecimalOrNull(trade.getQuantity()),
                toBigDecimalOrNull(trade.getPrice()),
                trade.getCurrency(),
                trade.getIntOrderId(),
                trade.getExtOrderId(),
                trade.getReservationRef(),
                trade.getBaseCurrency(),
                trade.getPortfolio(),
                getPortfolioName(trade.getPortfolio(), portfolioRepository),
                trade.getCounterPortfolio(),
                getPortfolioName(trade.getCounterPortfolio(), portfolioRepository),
                new BookingModel.RootExecution(trade.getRootOrderId(), trade.getRootExecutionId()),
                trade.getRootOrderId(),
                trade.getUnderlyingExecutionId(),
                trade.getParentOrderId(),
                trade.getSettled(),
                settledDateTime
            );
        }

        if (transaction.hasStreetCashTrade()) {
            StreetCashTradeSnapshot trade = transaction.getStreetCashTrade();
            String feeCurrency = mapFeeCurrency(trade.getTransactionFeeList());
            BigDecimal fee = mapFee(trade.getTransactionFeeList());

            Long settledDateTime = toOptionalDateTime(trade.getSettledDateTime());

            return new BookingModel.StreetCashTrade(
                trade.getUuid(),
                isoUtcTimeToLong(trade.getMetadata().getUpdatedAt()),
                trade.getDateTime(),
                trade.getExecutionId(),
                trade.getVenueExecutionId(),
                fee,
                feeCurrency,
                trade.getDescription(),
                toBigDecimalOrNull(trade.getQuantity()),
                toBigDecimalOrNull(trade.getPrice()),
                trade.getCurrency(),
                trade.getIntOrderId(),
                trade.getExtOrderId(),
                trade.getReservationRef(),
                trade.getBaseCurrency(),
                trade.getPortfolio(),
                getPortfolioName(trade.getPortfolio(), portfolioRepository),
                trade.getVenueAccount(),
                getVenueAccountName(trade.getVenueAccount()),
                new BookingModel.RootExecution(trade.getRootOrderId(), trade.getRootExecutionId()),
                trade.getRootOrderId(),
                trade.getUnderlyingExecutionId(),
                trade.getParentOrderId(),
                trade.getSettled(),
                settledDateTime
            );
        }

        if (transaction.hasClientAssetTrade()) {
            ClientAssetTradeSnapshot trade = transaction.getClientAssetTrade();
            String feeCurrency = mapFeeCurrency(trade.getTransactionFeeList());
            BigDecimal fee = mapFee(trade.getTransactionFeeList());

            Long settledDateTime = toOptionalDateTime(trade.getSettledDateTime());

            return new BookingModel.ClientAssetTrade(
                trade.getUuid(),
                isoUtcTimeToLong(trade.getMetadata().getUpdatedAt()),
                trade.getDateTime(),
                trade.getExecutionId(),
                trade.getVenueExecutionId(),
                fee,
                feeCurrency,
                trade.getDescription(),
                toBigDecimalOrNull(trade.getQuantity()),
                toBigDecimalOrNull(trade.getPrice()),
                trade.getCurrency(),
                trade.getIntOrderId(),
                trade.getExtOrderId(),
                trade.getReservationRef(),
                instrumentReferenceDataMapper.mapToInstrumentDto(trade.getInstrument()),
                trade.getPortfolio(),
                getPortfolioName(trade.getPortfolio(), portfolioRepository),
                trade.getCounterPortfolio(),
                getPortfolioName(trade.getCounterPortfolio(), portfolioRepository),
                new BookingModel.RootExecution(trade.getStreetSideOrderId(), trade.getStreetSideExecutionId()),
                trade.getSettled(),
                settledDateTime
            );
        }

        if (transaction.hasStreetAssetTrade()) {
            StreetAssetTradeSnapshot trade = transaction.getStreetAssetTrade();
            String feeCurrency = mapFeeCurrency(trade.getTransactionFeeList());
            BigDecimal fee = mapFee(trade.getTransactionFeeList());

            Long settledDateTime = toOptionalDateTime(trade.getSettledDateTime());

            return new BookingModel.StreetAssetTrade(
                trade.getUuid(),
                isoUtcTimeToLong(trade.getMetadata().getUpdatedAt()),
                trade.getDateTime(),
                trade.getExecutionId(),
                trade.getVenueExecutionId(),
                fee,
                feeCurrency,
                trade.getDescription(),
                toBigDecimalOrNull(trade.getQuantity()),
                toBigDecimalOrNull(trade.getPrice()),
                trade.getCurrency(),
                trade.getIntOrderId(),
                trade.getExtOrderId(),
                trade.getReservationRef(),
                instrumentReferenceDataMapper.mapToInstrumentDto(trade.getInstrument()),
                trade.getPortfolio(),
                getPortfolioName(trade.getPortfolio(), portfolioRepository),
                trade.getVenueAccount(),
                getVenueAccountName(trade.getVenueAccount()),
                trade.getClientSideOrderId(),
                trade.getSettled(),
                settledDateTime
            );
        }

        if (transaction.hasWithdrawal()) {
            WithdrawalSnapshot withdrawal = transaction.getWithdrawal();

            Long settledDateTime = toOptionalDateTime(withdrawal.getSettledDateTime());

            return new BookingModel.Withdrawal(
                withdrawal.getUuid(),
                isoUtcTimeToLong(withdrawal.getMetadata().getUpdatedAt()),
                withdrawal.getDateTime(),
                withdrawal.getExecutionId(),
                withdrawal.getVenueExecutionId(),
                withdrawal.getDescription(),
                toBigDecimalOrNull(withdrawal.getQuantity()),
                withdrawal.getCurrency(),
                withdrawal.getPortfolio(),
                getPortfolioName(withdrawal.getPortfolio(), portfolioRepository),
                withdrawal.getAccount(),
                getVenueAccountName(withdrawal.getAccount()),
                withdrawal.getSettled(),
                settledDateTime,
                withdrawal.getFeeAccountId(),
                getVenueAccountName(withdrawal.getFeeAccountId()),
                withdrawal.getFeePortfolioId(),
                getPortfolioName(withdrawal.getFeePortfolioId(), portfolioRepository)
            );
        }

        if (transaction.hasDeposit()) {
            DepositSnapshot deposit = transaction.getDeposit();

            Long settledDateTime = toOptionalDateTime(deposit.getSettledDateTime());

            return new BookingModel.Deposit(
                deposit.getUuid(),
                isoUtcTimeToLong(deposit.getMetadata().getUpdatedAt()),
                deposit.getDateTime(),
                deposit.getExecutionId(),
                deposit.getVenueExecutionId(),
                deposit.getDescription(),
                toBigDecimalOrNull(deposit.getQuantity()),
                deposit.getCurrency(),
                deposit.getPortfolio(),
                getPortfolioName(deposit.getPortfolio(), portfolioRepository),
                deposit.getAccount(),
                getVenueAccountName(deposit.getAccount()),
                deposit.getSettled(),
                settledDateTime,
                deposit.getFeeAccountId(),
                getVenueAccountName(deposit.getFeeAccountId()),
                deposit.getFeePortfolioId(),
                getPortfolioName(deposit.getFeePortfolioId(), portfolioRepository)
            );
        }

        if (transaction.hasAccountCashTransfer()) {
            AccountCashTransferSnapshot accountCashTransfer = transaction.getAccountCashTransfer();

            Long settledDateTime = toOptionalDateTime(accountCashTransfer.getSettledDateTime());

            return new BookingModel.AccountCashTransfer(
                accountCashTransfer.getUuid(),
                isoUtcTimeToLong(accountCashTransfer.getMetadata().getUpdatedAt()),
                accountCashTransfer.getDateTime(),
                accountCashTransfer.getExecutionId(),
                accountCashTransfer.getVenueExecutionId(),
                accountCashTransfer.getDescription(),
                toBigDecimalOrNull(accountCashTransfer.getQuantity()),
                accountCashTransfer.getCurrency(),
                accountCashTransfer.getFromAccountId(),
                getVenueAccountName(accountCashTransfer.getFromAccountId()),
                accountCashTransfer.getToAccountId(),
                getVenueAccountName(accountCashTransfer.getToAccountId()),
                accountCashTransfer.getSettled(),
                settledDateTime,
                accountCashTransfer.getFeeAccountId(),
                getVenueAccountName(accountCashTransfer.getFeeAccountId()),
                accountCashTransfer.getFeePortfolioId(),
                getPortfolioName(accountCashTransfer.getFeePortfolioId(), portfolioRepository)
            );
        }

        if (transaction.hasPortfolioCashTransfer()) {
            PortfolioCashTransferSnapshot portfolioCashTransfer = transaction.getPortfolioCashTransfer();

            Long settledDateTime = toOptionalDateTime(portfolioCashTransfer.getSettledDateTime());

            return new BookingModel.PortfolioCashTransfer(
                portfolioCashTransfer.getUuid(),
                isoUtcTimeToLong(portfolioCashTransfer.getMetadata().getUpdatedAt()),
                portfolioCashTransfer.getDateTime(),
                portfolioCashTransfer.getExecutionId(),
                portfolioCashTransfer.getVenueExecutionId(),
                portfolioCashTransfer.getDescription(),
                toBigDecimalOrNull(portfolioCashTransfer.getQuantity()),
                portfolioCashTransfer.getCurrency(),
                portfolioCashTransfer.getFromPortfolioId(),
                getPortfolioName(portfolioCashTransfer.getFromPortfolioId(), portfolioRepository),
                portfolioCashTransfer.getToPortfolioId(),
                getPortfolioName(portfolioCashTransfer.getToPortfolioId(), portfolioRepository),
                portfolioCashTransfer.getSettled(),
                settledDateTime,
                portfolioCashTransfer.getFeePortfolioId(),
                getPortfolioName(portfolioCashTransfer.getFeePortfolioId(), portfolioRepository)
            );
        }

        if (transaction.hasSettlement()) {
            SettlementSnapshot settlement = transaction.getSettlement();

            return new BookingModel.Settlement(
                settlement.getUuid(),
                isoUtcTimeToLong(settlement.getMetadata().getUpdatedAt()),
                settlement.getDateTime(),
                settlement.getDescription(),
                settlement.getSettledTransactionIdsList()
            );
        }

        if (transaction.hasFee()) {
            FeeSnapshot fee = transaction.getFee();

            Long settledDateTime = toOptionalDateTime(fee.getSettledDateTime());

            return new BookingModel.Fee(
                fee.getUuid(),
                isoUtcTimeToLong(fee.getMetadata().getUpdatedAt()),
                fee.getDateTime(),
                fee.getExecutionId(),
                fee.getVenueExecutionId(),
                fee.getDescription(),
                toBigDecimalOrNull(fee.getQuantity()),
                fee.getCurrency(),
                fee.getPortfolioId(),
                getPortfolioName(fee.getPortfolioId(), portfolioRepository),
                fee.getAccountId(),
                getVenueAccountName(fee.getAccountId()),
                fee.getOrderId(),
                fee.getParentOrderId(),
                fee.getUnderlyingExecutionId(),
                new BookingModel.RootExecution(fee.getRootOrderId(), fee.getRootExecutionId()),
                fee.getSettled(),
                settledDateTime
            );
        }

        LOGGER.error("Unsupported transaction type. Transaction will be skipped: ({})", transaction);
        return null;
    }

    private String getVenueAccountName(String venueAccountId) {
        return venueAccountService.getVenueAccountName(venueAccountId);
    }

    private Long toOptionalDateTime(String dateTime) {
        if (dateTime == null || dateTime.isEmpty()) {
            return null;
        }

        return isoUtcTimeToLong(dateTime);
    }

    private BigDecimal mapFee(Collection<Fee> fees) {
        if (fees.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // to be replaced with fee list once UI is ready
        Fee anyFee = fees.iterator().next();
        return toBigDecimalOrNull(anyFee.getAmount());
    }

    private String mapFeeCurrency(Collection<Fee> fees) {
        if (fees.isEmpty()) {
            return "";
        }

        // to be replaced with fee list once UI is ready
        Fee anyFee = fees.iterator().next();
        return anyFee.getCurrency();
    }
}



package io.wyden.brokerconfig.domain;

import io.wyden.published.brokerdesk.CurrencyType;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.oems.FeeBasis;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.FeeType;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.rate.Rate;
import io.wyden.rate.client.RatesCacheFacade;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isCreatable;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isNonZero;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isPositive;
import static io.wyden.cloudutils.tools.BigDecimalUtils.max;
import static io.wyden.cloudutils.tools.BigDecimalUtils.min;
import static io.wyden.cloudutils.tools.MathUtils.divide;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class ReservationFeeProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReservationFeeProcessor.class);

    private final RatesCacheFacade ratesCacheFacade;
    private final boolean failOnMissingRate;

    public ReservationFeeProcessor(
        RatesCacheFacade ratesCacheFacade,
        @Value("${reservation.fee.fail-on-missing-rate:true}") boolean failOnMissingRate) {
        this.ratesCacheFacade = ratesCacheFacade;
        this.failOnMissingRate = failOnMissingRate;
    }

    /**
     * Calculates reservation fees based on the provided OEMS request.
     *
     * @param oemsRequest The OEMS request containing order details
     * @return Collection of fee data objects
     * @throws IllegalArgumentException if required parameters are missing or invalid
     * @throws IllegalStateException if rate information cannot be retrieved and failOnMissingRate is true
     */
    public Collection<FeeData> calculateReservationFees(OemsRequest oemsRequest) {
        validateOemsRequest(oemsRequest);

        ExecutionConfig executionConfig = oemsRequest.getExecutionConfig();

        Collection<FeeData> fees = new ArrayList<>();

        // Process fixed fee first (no exchange rate needed)
        processFixedFee(executionConfig, fees);

        // Process percentage and minimum fees only if needed
        if (hasPercentageOrMinFeeConfiguration(executionConfig)) {
            processPercentageAndMinimumFees(oemsRequest, executionConfig, fees);
        }

        return fees;
    }

    private void validateOemsRequest(OemsRequest oemsRequest) {
        if (oemsRequest == null) {
            throw new IllegalArgumentException("OemsRequest cannot be null");
        }

        if (StringUtils.isBlank(oemsRequest.getBaseCurrency())) {
            throw new IllegalArgumentException("Base currency is required");
        }
        if (StringUtils.isBlank(oemsRequest.getQuoteCurrency())) {
            throw new IllegalArgumentException("Quote currency is required");
        }
        if (StringUtils.isBlank(oemsRequest.getQuantity())) {
            throw new IllegalArgumentException("Quantity is required");
        }
        if (oemsRequest.getOrderType() == OemsOrderType.ORDER_TYPE_UNSPECIFIED) {
            throw new IllegalArgumentException("Order type is required");
        }
        if (oemsRequest.getSide() == OemsSide.SIDE_UNDETERMINED) {
            throw new IllegalArgumentException("Order side is required");
        }

        // Validate order type specific requirements
        OemsOrderType orderType = oemsRequest.getOrderType();

        if ((orderType == OemsOrderType.LIMIT || orderType == OemsOrderType.STOP_LIMIT)
            && StringUtils.isBlank(oemsRequest.getPrice())) {
            throw new IllegalArgumentException("Price is required for " + orderType + " orders");
        }

        if ((orderType == OemsOrderType.STOP || orderType == OemsOrderType.STOP_LIMIT)
            && StringUtils.isBlank(oemsRequest.getStopPrice())) {
            throw new IllegalArgumentException("Stop price is required for " + orderType + " orders");
        }
    }

    private BigDecimal getQuantity(OemsRequest oemsRequest) {
        BigDecimal quantity = bd(oemsRequest.getQuantity());
        if (quantity == null) {
            throw new IllegalArgumentException("Invalid quantity format");
        }

        boolean isBuyingSide = isBuyingSide(oemsRequest.getSide());
        if (!isBuyingSide) {
            return quantity.negate();
        }

        return quantity;
    }

    private static boolean isBuyingSide(OemsSide side) {
        return side == OemsSide.BUY || side == OemsSide.REDUCE_SHORT;
    }

    /**
     * Checks if the execution config has percentage or minimum fee configuration.
     * These require exchange rate calculations.
     *
     * @param executionConfig The execution configuration to check
     * @return true if percentage or minimum fee configuration is present
     */
    private boolean hasPercentageOrMinFeeConfiguration(ExecutionConfig executionConfig) {
        return hasMinFeeConfiguration(executionConfig) || hasPercentageFeeConfiguration(executionConfig);
    }

    /**
     * Checks if fixed fee configuration is present.
     */
    private boolean hasFixedFeeConfiguration(ExecutionConfig executionConfig) {
        return isCreatable(executionConfig.getFixedFee()) &&
            bd(executionConfig.getFixedFee()).compareTo(BigDecimal.ZERO) > 0 &&
            StringUtils.isNotBlank(executionConfig.getFixedFeeCurrency());
    }

    /**
     * Checks if minimum fee configuration is present.
     */
    private boolean hasMinFeeConfiguration(ExecutionConfig executionConfig) {
        return isCreatable(executionConfig.getMinFee()) &&
            bd(executionConfig.getMinFee()).compareTo(BigDecimal.ZERO) > 0 &&
            StringUtils.isNotBlank(executionConfig.getMinFeeCurrency());
    }

    /**
     * Checks if percentage fee configuration is present.
     */
    private boolean hasPercentageFeeConfiguration(ExecutionConfig executionConfig) {
        return isCreatable(executionConfig.getPercentageFee()) &&
            bd(executionConfig.getPercentageFee()).compareTo(BigDecimal.ZERO) > 0 &&
            executionConfig.getPercentageFeeCurrencyType() != CurrencyType.CURRENCY_TYPE_UNSPECIFIED;
    }

    private BigDecimal getPrice(OemsRequest oemsRequest) {
        String oemsRequestPrice = oemsRequest.getPrice();

        if (isNotBlank(oemsRequestPrice)) {
            BigDecimal price = bd(oemsRequestPrice);
            if (isNonZero(price)) {
                return price;
            }
        }

        return getExchangeRate(oemsRequest.getBaseCurrency(), oemsRequest.getQuoteCurrency());
    }

    private BigDecimal getStopPrice(OemsRequest oemsRequest) {
        String stopPrice = oemsRequest.getStopPrice();
        if (StringUtils.isBlank(stopPrice)) {
            return null;
        }

        BigDecimal result = bd(stopPrice);
        if (result == null) {
            throw new IllegalArgumentException("Invalid stop price format");
        }

        return result;
    }

    private BigDecimal calculateEstimatedExecutionValue(OemsRequest oemsRequest, BigDecimal quantity, BigDecimal price, BigDecimal stopPrice) {
        try {
            BigDecimal marketPrice = getExchangeRate(oemsRequest.getBaseCurrency(), oemsRequest.getQuoteCurrency());
            BigDecimal executionAmount = calculateEstimatedExecutionValueByOrderType(quantity, oemsRequest.getOrderType(),
                marketPrice, price, stopPrice);
            return executionAmount.abs();
        } catch (IllegalStateException e) {
            throw e; // Re-throw rate-related exceptions
        } catch (Exception e) {
            throw new IllegalStateException("Exception occurred during calculating estimated execution price", e);
        }
    }

    private BigDecimal calculateEstimatedExecutionValueByOrderType(BigDecimal quantity, OemsOrderType orderType,
                                                                   BigDecimal marketPrice, BigDecimal price, BigDecimal stopPrice) {
        BigDecimal orderMarketValue = quantity.multiply(marketPrice);

        return switch (orderType) {
            case MARKET -> orderMarketValue;
            case LIMIT -> {
                BigDecimal orderNotionalValue = quantity.multiply(price);
                yield isPositive(quantity)
                    ? orderNotionalValue.min(orderMarketValue)
                    : orderNotionalValue.max(orderMarketValue);
            }
            case STOP -> isPositive(quantity)
                ? max(quantity.multiply(stopPrice), orderMarketValue)
                : min(quantity.multiply(stopPrice), orderMarketValue);
            case STOP_LIMIT -> {
                BigDecimal orderNotionalValue = quantity.multiply(price);
                BigDecimal stopValue = quantity.multiply(stopPrice);
                yield isPositive(quantity)
                    ? min(orderNotionalValue, max(stopValue, orderMarketValue))
                    : max(orderNotionalValue, min(stopValue, orderMarketValue));
            }
            case UNRECOGNIZED, ORDER_TYPE_UNSPECIFIED -> throw new IllegalArgumentException("Order type %s not supported".formatted(orderType));
        };
    }

    private String determinePercentageFeeCurrency(OemsRequest oemsRequest, ExecutionConfig executionConfig) {
        CurrencyType percentageFeeCurrencyType = executionConfig.getPercentageFeeCurrencyType();

        if (percentageFeeCurrencyType == CurrencyType.QUOTE_CURRENCY) {
            return oemsRequest.getQuoteCurrency();
        } else if (percentageFeeCurrencyType == CurrencyType.BASE_CURRENCY) {
            return oemsRequest.getBaseCurrency();
        } else if (percentageFeeCurrencyType == CurrencyType.SPECIFIC_CURRENCY) {
            String percentageFeeCurrency = executionConfig.getPercentageFeeCurrency();
            if (StringUtils.isBlank(percentageFeeCurrency)) {
                throw new IllegalArgumentException("Percentage fee currency is required when currency type is SPECIFIC_CURRENCY");
            }
            return percentageFeeCurrency;
        } else {
            // Default to quote currency if unspecified
            LOGGER.warn("Unspecified percentage fee currency type for request: {}, defaulting to quote currency",
                oemsRequest.getOrderId());
            return oemsRequest.getQuoteCurrency();
        }
    }

    private BigDecimal calculateMinFeeInPercentageFeeCurrency(String minFee, String minFeeCurrency, String percentageFeeCurrency) {
        if (!isCreatable(minFee) || StringUtils.isBlank(minFeeCurrency)) {
            return BigDecimal.ZERO;
        }

        if (minFeeCurrency.equals(percentageFeeCurrency)) {
            return bd(minFee);
        }

        try {
            BigDecimal rate = getExchangeRate(minFeeCurrency, percentageFeeCurrency);
            return bd(minFee).multiply(rate);
        } catch (IllegalStateException e) {
            throw e; // Re-throw rate-related exceptions
        } catch (Exception e) {
            throw new IllegalStateException("Error when calculating minFee in fee currency", e);
        }
    }

    private BigDecimal calculatePercFeeInPercFeeCurrency(String quoteCurrency, String percentageFee, String percentageFeeCurrency,
                                                         CurrencyType currencyType, BigDecimal estimatedExecutionValue) {
        if (currencyType == CurrencyType.CURRENCY_TYPE_UNSPECIFIED || !isCreatable(percentageFee)) {
            return BigDecimal.ZERO;
        }

        try {
            BigDecimal percentageFeeRate = bd(percentageFee);
            if (currencyType == CurrencyType.QUOTE_CURRENCY) {
                return estimatedExecutionValue.multiply(percentageFeeRate);
            } else if (currencyType == CurrencyType.BASE_CURRENCY || currencyType == CurrencyType.SPECIFIC_CURRENCY) {
                BigDecimal rate = getExchangeRate(quoteCurrency, percentageFeeCurrency);
                return estimatedExecutionValue.multiply(percentageFeeRate).multiply(rate);
            } else {
                throw new IllegalArgumentException("Unspecified currencyType: %s".formatted(currencyType));
            }
        } catch (IllegalStateException e) {
            throw e; // Re-throw rate-related exceptions
        } catch (Exception e) {
            throw new IllegalStateException("Error when calculating percentage fee", e);
        }
    }

    private BigDecimal getExchangeRate(String baseCurrency, String quoteCurrency) {
        Optional<Rate> rateOptional = ratesCacheFacade.find(baseCurrency, quoteCurrency);
        if (rateOptional.isEmpty()) {
            if (!failOnMissingRate) {
                LOGGER.info("Rate {}/{} not found, falling back to zero", baseCurrency, quoteCurrency);
                return BigDecimal.ZERO;
            }
            throw new IllegalStateException("Rate %s/%s not found".formatted(baseCurrency, quoteCurrency));
        }
        BigDecimal rate = new BigDecimal(rateOptional.get().getValue());
        LOGGER.trace("Rate {}/{}: {}", baseCurrency, quoteCurrency, rate);
        return rate;
    }

    /**
     * Process fixed fee and add to the fees collection if applicable.
     * This doesn't require any exchange rate lookups.
     */
    private void processFixedFee(ExecutionConfig executionConfig, Collection<FeeData> fees) {
        if (hasFixedFeeConfiguration(executionConfig)) {
            fees.add(createFixedFee(executionConfig));
            LOGGER.debug("Added fixed fee: {} {}", executionConfig.getFixedFee(), executionConfig.getFixedFeeCurrency());
        }
    }

    /**
     * Process percentage and minimum fees, which may require exchange rate lookups.
     */
    private void processPercentageAndMinimumFees(OemsRequest oemsRequest, ExecutionConfig executionConfig, Collection<FeeData> fees) {
        try {
            // Extract and prepare all necessary parameters
            BigDecimal quantity = getQuantity(oemsRequest);
            BigDecimal price = getPrice(oemsRequest);
            BigDecimal stopPrice = getStopPrice(oemsRequest);

            // Adjust quantity for cash orders
            boolean isCashOrder = oemsRequest.getCurrency().equals(oemsRequest.getQuoteCurrency());
            if (isCashOrder) {
                quantity = divide(quantity, price);
            }

            // Calculate estimated execution price for percentage fee
            BigDecimal estimatedExecutionValue = calculateEstimatedExecutionValue(oemsRequest, quantity, price, stopPrice);
            String percentageFeeCurrency = determinePercentageFeeCurrency(oemsRequest, executionConfig);
            BigDecimal percentageFeeAmount = calculatePercFeeInPercFeeCurrency(
                oemsRequest.getQuoteCurrency(),
                executionConfig.getPercentageFee(),
                percentageFeeCurrency,
                executionConfig.getPercentageFeeCurrencyType(),
                estimatedExecutionValue
            );

            // If no min fee is configured, just add percentage fee
            if (!hasMinFeeConfiguration(executionConfig)) {
                fees.add(createPercentageFee(percentageFeeCurrency, percentageFeeAmount));
                LOGGER.debug("Added percentage fee: {} {}", percentageFeeAmount, percentageFeeCurrency);
                return;
            }

            // Both percentage and min fee are configured - compare them
            BigDecimal minFeeInPercentageFeeCurrency = calculateMinFeeInPercentageFeeCurrency(
                executionConfig.getMinFee(),
                executionConfig.getMinFeeCurrency(),
                percentageFeeCurrency
            );

            // Add the higher of the two fees
            if (percentageFeeAmount.compareTo(minFeeInPercentageFeeCurrency) <= 0) {
                fees.add(createPercentageFee(percentageFeeCurrency, minFeeInPercentageFeeCurrency));
                LOGGER.debug("Added minimum fee: {} {} (percentage fee {} {} was lower)",
                    minFeeInPercentageFeeCurrency, percentageFeeCurrency,
                    percentageFeeAmount, percentageFeeCurrency);
            } else {
                fees.add(createPercentageFee(percentageFeeCurrency, percentageFeeAmount));
                LOGGER.debug("Added percentage fee: {} {}", percentageFeeAmount, percentageFeeCurrency);
            }
        } catch (IllegalStateException e) {
            LOGGER.error("Error processing percentage or minimum fees: {}", e.getMessage());
            throw e;
        }
    }

    private FeeData createFixedFee(ExecutionConfig executionConfig) {
        return FeeData.newBuilder()
            .setCurrency(executionConfig.getFixedFeeCurrency())
            .setAmount(executionConfig.getFixedFee())
            .setType(FeeType.FIXED_FEE)
            .setBasis(FeeBasis.ABSOLUTE)
            .build();
    }

    private FeeData createMinimumFee(ExecutionConfig executionConfig) {
        return FeeData.newBuilder()
            .setCurrency(executionConfig.getMinFeeCurrency())
            .setAmount(executionConfig.getMinFee())
            .setType(FeeType.TRANSACTION_FEE)
            .setBasis(FeeBasis.ABSOLUTE)
            .build();
    }

    private FeeData createPercentageFee(String currency, BigDecimal amount) {
        return FeeData.newBuilder()
            .setCurrency(currency)
            .setAmount(amount.toString())
            .setType(FeeType.TRANSACTION_FEE)
            .setBasis(FeeBasis.ABSOLUTE)
            .build();
    }

}

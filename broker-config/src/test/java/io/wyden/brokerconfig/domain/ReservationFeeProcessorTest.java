package io.wyden.brokerconfig.domain;

import io.wyden.published.brokerdesk.CurrencyType;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.oems.FeeBasis;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.FeeType;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.rate.Rate;
import io.wyden.rate.client.RatesCacheFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Optional;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ReservationFeeProcessorTest {

    private static final String FIXED_FEE_CURRENCY = "XYZ";
    private static final String FIXED_FEE = "10";
    private static final String PERC_FEE = "0.02";
    private static final String BASE_CURRENCY = "BTC";
    private static final String QUOTE_CURRENCY = "USD";
    private static final BigDecimal BTC_USD_RATE = bd(60000);
    private static final BigDecimal GBP_USD_RATE = bd(1.35);
    private static final String SPECIFIC_CURRENCY = "TRY";
    private static final String MIN_FEE = "5";
    private static final String MIN_FEE_CURRENCY = "GBP";
    private RatesCacheFacade ratesCacheFacade;

    private ReservationFeeProcessor reservationFeeProcessor;

    @BeforeEach
    public void setUp() {
        ratesCacheFacade = mock(RatesCacheFacade.class);
        reservationFeeProcessor = new ReservationFeeProcessor(ratesCacheFacade, false);
    }

    @ParameterizedTest
    @EnumSource(value = OemsSide.class, names = {"UNRECOGNIZED", "SIDE_UNDETERMINED"}, mode = EnumSource.Mode.EXCLUDE)
    public void testCalculateReservationFees_withFixedFee(OemsSide side) {
        // Given
        OemsRequest oemsRequest = createBasicOemsRequest(side);
        ExecutionConfig executionConfig = ExecutionConfig.newBuilder()
            .setFixedFee(FIXED_FEE)
            .setFixedFeeCurrency(FIXED_FEE_CURRENCY)
            .setPercentageFee("0")
            .build();

        oemsRequest = oemsRequest.toBuilder()
            .setExecutionConfig(executionConfig)
            .build();

        mockRate(BASE_CURRENCY, QUOTE_CURRENCY, BTC_USD_RATE.toString());

        // When
        Collection<FeeData> fees = reservationFeeProcessor.calculateReservationFees(oemsRequest);

        // Then
        assertThat(fees).hasSize(1);
        FeeData fee = fees.iterator().next();
        assertThat(fee.getAmount()).isEqualTo(FIXED_FEE);
        assertThat(fee.getCurrency()).isEqualTo(FIXED_FEE_CURRENCY);
        assertThat(fee.getType()).isEqualTo(FeeType.FIXED_FEE);
        assertThat(fee.getBasis()).isEqualTo(FeeBasis.ABSOLUTE);
    }

    @ParameterizedTest
    @EnumSource(value = OemsSide.class, names = {"UNRECOGNIZED", "SIDE_UNDETERMINED"}, mode = EnumSource.Mode.EXCLUDE)
    public void testCalculateReservationFees_withPercentageFeeInQuoteCurrency(OemsSide side) {
        // Given
        mockRate(BASE_CURRENCY, QUOTE_CURRENCY, BTC_USD_RATE.toString());

        OemsRequest oemsRequest = createBasicOemsRequest(side);
        ExecutionConfig executionConfig = createExecutionConfig()
            .toBuilder()
            .setFixedFee("0")
            .setPercentageFee(PERC_FEE)
            .setPercentageFeeCurrencyType(CurrencyType.QUOTE_CURRENCY)
            .build();

        oemsRequest = oemsRequest.toBuilder()
            .setExecutionConfig(executionConfig)
            .setQuantity("0.01")
            .build();

        // When
        Collection<FeeData> fees = reservationFeeProcessor.calculateReservationFees(oemsRequest);

        // Then
        assertThat(fees).hasSize(1);
        FeeData fee = fees.iterator().next();
        assertThat(new BigDecimal(fee.getAmount())).isEqualByComparingTo(new BigDecimal("12")); // 0.02 * 0.01 * 60000 = 12
        assertThat(fee.getCurrency()).isEqualTo(QUOTE_CURRENCY);
        assertThat(fee.getType()).isEqualTo(FeeType.TRANSACTION_FEE);
        assertThat(fee.getBasis()).isEqualTo(FeeBasis.ABSOLUTE);
    }

    @ParameterizedTest
    @EnumSource(value = OemsSide.class, names = {"UNRECOGNIZED", "SIDE_UNDETERMINED"}, mode = EnumSource.Mode.EXCLUDE)
    public void testCalculateReservationFees_withPercentageFeeInOtherCurrency(OemsSide side) {
        // Given
        mockRate(BASE_CURRENCY, QUOTE_CURRENCY, BTC_USD_RATE.toString());
        mockRate(QUOTE_CURRENCY, SPECIFIC_CURRENCY, "33");

        OemsRequest oemsRequest = createBasicOemsRequest(side);
        ExecutionConfig executionConfig = createExecutionConfig()
            .toBuilder()
            .setFixedFee("0")
            .setPercentageFee(PERC_FEE)
            .setPercentageFeeCurrency(SPECIFIC_CURRENCY)
            .setPercentageFeeCurrencyType(CurrencyType.SPECIFIC_CURRENCY)
            .build();

        oemsRequest = oemsRequest.toBuilder()
            .setExecutionConfig(executionConfig)
            .setQuantity("0.01")
            .build();

        // When
        Collection<FeeData> fees = reservationFeeProcessor.calculateReservationFees(oemsRequest);

        // Then
        assertThat(fees).hasSize(1);
        FeeData fee = fees.iterator().next();
        assertThat(new BigDecimal(fee.getAmount())).isEqualByComparingTo(new BigDecimal("396")); // 0.02 * 0.01 * 60000 * 33 = 396
        assertThat(fee.getCurrency()).isEqualTo(SPECIFIC_CURRENCY);
        assertThat(fee.getType()).isEqualTo(FeeType.TRANSACTION_FEE);
        assertThat(fee.getBasis()).isEqualTo(FeeBasis.ABSOLUTE);
    }

    @ParameterizedTest
    @EnumSource(value = OemsSide.class, names = {"UNRECOGNIZED", "SIDE_UNDETERMINED"}, mode = EnumSource.Mode.EXCLUDE)
    public void testCalculateReservationFees_withMinFee(OemsSide oemsSide) {
        // Given
        mockRate(BASE_CURRENCY, QUOTE_CURRENCY, BTC_USD_RATE.toString());
        mockRate(MIN_FEE_CURRENCY, QUOTE_CURRENCY, GBP_USD_RATE.toString());

        OemsRequest oemsRequest = createBasicOemsRequest(oemsSide);
        ExecutionConfig executionConfig = createExecutionConfig()
            .toBuilder()
            .setFixedFee("0")
            .setPercentageFee("0.0001") // Very small percentage to ensure min fee applies
            .setMinFee(MIN_FEE)
            .setMinFeeCurrency(MIN_FEE_CURRENCY)
            .build();

        oemsRequest = oemsRequest.toBuilder()
            .setExecutionConfig(executionConfig)
            .setQuantity("0.01")
            .build();

        // When
        Collection<FeeData> fees = reservationFeeProcessor.calculateReservationFees(oemsRequest);

        // Then
        assertThat(fees).hasSize(1);
        FeeData fee = fees.iterator().next();
        assertThat(fee.getAmount()).isEqualTo("6.75"); // 5 * 1.35 = 6.75
        assertThat(fee.getCurrency()).isEqualTo(QUOTE_CURRENCY);
        assertThat(fee.getType()).isEqualTo(FeeType.TRANSACTION_FEE);
        assertThat(fee.getBasis()).isEqualTo(FeeBasis.ABSOLUTE);
    }

    @ParameterizedTest
    @EnumSource(value = OemsSide.class, names = {"UNRECOGNIZED", "SIDE_UNDETERMINED"}, mode = EnumSource.Mode.EXCLUDE)
    public void testCalculateReservationFees_limitOrderWithPercentageFeeInQuoteCurrency(OemsSide side) {
        // Given
        mockRate(BASE_CURRENCY, QUOTE_CURRENCY, BTC_USD_RATE.toString());

        OemsRequest oemsRequest = createBasicOemsRequest(side);
        ExecutionConfig executionConfig = createExecutionConfig()
            .toBuilder()
            .setFixedFee("0")
            .setPercentageFee(PERC_FEE)
            .setPercentageFeeCurrencyType(CurrencyType.QUOTE_CURRENCY)
            .build();

        oemsRequest = oemsRequest.toBuilder()
            .setExecutionConfig(executionConfig)
            .setOrderType(OemsOrderType.LIMIT)
            .setQuantity("0.01")
            .setPrice("58000")
            .build();

        // When
        Collection<FeeData> fees = reservationFeeProcessor.calculateReservationFees(oemsRequest);

        // Then
        assertThat(fees).hasSize(1);
        FeeData fee = fees.iterator().next();
        assertThat(new BigDecimal(fee.getAmount())).isEqualByComparingTo(bd("11.6")); // 0.02 * 0.01 * 58000 = 11.6
        assertThat(fee.getCurrency()).isEqualTo(QUOTE_CURRENCY);
        assertThat(fee.getType()).isEqualTo(FeeType.TRANSACTION_FEE);
        assertThat(fee.getBasis()).isEqualTo(FeeBasis.ABSOLUTE);
    }

    private OemsRequest createBasicOemsRequest(OemsSide oemsSide) {
        return OemsRequest.newBuilder()
            .setOrderId("order123")
            .setBaseCurrency(BASE_CURRENCY)
            .setQuoteCurrency(QUOTE_CURRENCY)
            .setCurrency(BASE_CURRENCY)
            .setQuantity("1.5")
            .setOrderType(OemsOrderType.MARKET)
            .setSide(oemsSide)
            .setExecutionConfig(createExecutionConfig())
            .build();
    }

    private ExecutionConfig createExecutionConfig() {
        return ExecutionConfig.newBuilder()
            .setPercentageFee(PERC_FEE)
            .setPercentageFeeCurrencyType(CurrencyType.QUOTE_CURRENCY)
            .setFixedFee(FIXED_FEE)
            .setFixedFeeCurrency(QUOTE_CURRENCY)
            .setMinFee(MIN_FEE)
            .setMinFeeCurrency(QUOTE_CURRENCY)
            .build();
    }

    private void mockRate(String baseCurrency, String quoteCurrency, String rate) {
        Rate rateObj = Rate.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setValue(rate)
            .build();

        when(ratesCacheFacade.find(eq(baseCurrency), eq(quoteCurrency)))
            .thenReturn(Optional.of(rateObj));
    }
}
{"name": "wyden", "private": true, "version": "2025.4.0", "description": "Wyden.io", "main": "index.js", "scripts": {"dev": "vite", "qa": "vite --mode qa", "uat": "vite --mode uat", "demo": "vite --mode demo", "garanti-uat": "vite --mode garanti-uat", "compose": "vite --mode compose", "build": "vite build && npx eslint './src/**/*.{js,ts,tsx}' && npm run codegen && npm run typecheck", "generateUd": "ts-to-zod src/wyden/hooks/useUserData/types.ts src/wyden/hooks/useUserData/generated.ts", "build-development": "tsc && vite build --mode development", "build-compose": "tsc && vite build --mode compose", "test:watch": "TZ=UTC VITE_REST_API_SERVER=http://localhost:3000 vitest", "test": "TZ=UTC VITE_REST_API_SERVER=http://localhost:3000 vitest run", "test:cypress": "TZ=UTC npx cypress run", "test:cypress-component": "TZ=UTC npx cypress run --component --config video=false", "test:cypress-component-report-portal": "REPORT_PORTAL=true npm run test:cypress-component", "test:cypress-component:changed": "./run_cyct_changed_tests.sh", "test:open-cypress": "TZ=UTC npx cypress open", "preview": "vite preview --port 3000", "vis": "rollup-plugin-visualiser", "typecheck": "tsc --noEmit", "codegen": "graphql-codegen --config ./src/wyden/codegen.yml && prettier 'src/wyden/services/graphql/generated/graphql.tsx' --write", "test:ci": "npm run test && npm run test:cypress-component", "test:ci:changed": "npm run test && npm run test:cypress-component:changed", "validate": "npx eslint './src/**/*.{js,ts,tsx}' && npm run codegen && npm run typecheck && npm run test && npm run test:cypress-component", "beautify": "prettier --list-different './src/**/*.{js,ts,tsx}' --write", "beautify:all": "prettier . './src/**/*.{js,ts,tsx}' --write", "lint-staged": "npx lint-staged", "prepare": "husky install", "pre-push-validate": "npm run lint-staged", "storybook": "start-storybook -p 6006"}, "lint-staged": {"./src/**/*.{js,ts,tsx}": ["typecheck", "eslint --fix", "prettier --write"]}, "author": "", "license": "ISC", "dependencies": {"@algotrader/nest": "2.8.0", "@apollo/client": "3.11.10", "@babel/core": "7.22.20", "@emotion/babel-plugin": "11.11.0", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@graphql-codegen/cli": "5.0.0", "@graphql-codegen/introspection": "4.0.0", "@graphql-codegen/typescript": "4.0.1", "@graphql-codegen/typescript-operations": "4.0.1", "@graphql-codegen/typescript-react-apollo": "3.3.7", "@hookform/resolvers": "3.3.1", "@mui/base": "5.0.0-beta.33", "@mui/icons-material": "5.14.9", "@mui/lab": "5.0.0-alpha.145", "@mui/material": "5.15.6", "@mui/styles": "6.4.0", "@mui/x-date-pickers": "6.19.6", "@reduxjs/toolkit": "1.9.5", "@sentry/react": "8.15.0", "@testing-library/react-hooks": "8.0.1", "@ts-react/form": "1.8.3", "@types/node": "20.6.2", "@vitejs/plugin-react": "4.0.4", "ag-grid-community": "28.2.1", "ag-grid-enterprise": "28.2.1", "ag-grid-react": "28.2.1", "axios": "1.5.0", "babel-loader": "9.1.3", "big.js": "6.2.1", "cross-env": "7.0.3", "cross-fetch": "4.0.0", "css-element-queries": "1.2.3", "date-fns": "2.30.0", "dotenv": "16.4.7", "eslint-plugin-react-hooks": "4.6.0", "graphql": "16.8.0", "graphql-codegen-typescript-validation-schema": "0.11.1", "graphql-ws": "5.14.0", "html2canvas": "1.4.1", "i18next": "23.5.1", "i18next-http-backend": "1.4.1", "intro.js": "7.2.0", "intro.js-react": "1.0.0", "jsdom": "22.1.0", "jszip": "3.10.1", "keycloak-js": "19.0.2", "lodash": "4.17.21", "material-ui-popup-state": "5.3.3", "notistack": "3.0.1", "posthog-js": "1.174.2", "react": "18.2.0", "react-dom": "18.2.0", "react-draggable": "4.4.6", "react-error-boundary": "4.0.11", "react-hook-form": "7.49.3", "react-i18next": "13.2.2", "react-redux": "8.1.1", "react-router-dom": "6.16.0", "rollup-plugin-visualizer": "5.6.0", "rrweb": "2.0.0-alpha.4", "rrweb-player": "1.0.0-alpha.4", "use-resize-observer": "9.1.0", "uuid": "9.0.0", "vite": "4.4.9", "vite-plugin-svgr": "3.2.0", "zod": "3.22.2", "zustand": "4.5.5"}, "devDependencies": {"@algotrader/schema-graphql": "1.0.333", "@reportportal/agent-js-cypress": "5.3.5", "@storybook/addon-actions": "6.5.15", "@storybook/addon-essentials": "6.5.15", "@storybook/addon-interactions": "6.5.15", "@storybook/addon-links": "6.5.15", "@storybook/builder-vite": "0.1.38", "@storybook/react": "6.5.15", "@storybook/testing-library": "0.0.13", "@testing-library/cypress": "10.0.1", "@testing-library/dom": "9.3.3", "@testing-library/jest-dom": "6.1.3", "@testing-library/react": "13.4.0", "@testing-library/user-event": "14.5.1", "@types/big.js": "6.2.2", "@types/lodash": "4.14.198", "@types/react": "18.0.0", "@types/react-dom": "18.0.0", "@types/redux-mock-store": "1.0.3", "@types/uuid": "8.3.4", "@typescript-eslint/eslint-plugin": "6.7.0", "@typescript-eslint/parser": "6.7.0", "@zendesk/laika": "1.4.1", "apollo-link": "1.2.14", "concurrently": "7.6.0", "cryptocurrency-icons": "0.18.1", "cypress": "13.2.0", "eslint": "8.18.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-react": "7.30.1", "graphql-codegen-typescript-mock-data": "3.2.2", "husky": "8.0.1", "lint-staged": "14.0.1", "msw": "1.3.1", "prettier": "3.0.3", "redux-mock-store": "1.5.4", "ts-to-zod": "3.1.3", "typescript": "5.2", "vitest": "0.34.4"}, "engines": {"npm": ">=8.19.2", "node": ">=18.0.0"}, "msw": {"workerDirectory": "public"}}
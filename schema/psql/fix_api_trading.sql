--
-- PostgreSQL database dump
--

-- Dumped from database version 16.2 (Debian 16.2-1.pgdg120+2)
-- Dumped by pg_dump version 16.2 (Debian 16.2-1.pgdg120+2)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: event_backup_log_sequence; Type: SEQUENCE; Schema: public; Owner: fix_api_trading
--

CREATE SEQUENCE public.event_backup_log_sequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.event_backup_log_sequence OWNER TO fix_api_trading;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: event_backup_log; Type: TABLE; Schema: public; Owner: fix_api_trading
--

CREATE TABLE public.event_backup_log (
    id integer DEFAULT nextval('public.event_backup_log_sequence'::regclass) NOT NULL,
    "time" timestamp without time zone NOT NULL,
    beginstring character(8),
    sendercompid character varying(64),
    targetcompid character varying(64),
    session_qualifier character varying(64),
    text text NOT NULL
);


ALTER TABLE public.event_backup_log OWNER TO fix_api_trading;

--
-- Name: event_log_sequence; Type: SEQUENCE; Schema: public; Owner: fix_api_trading
--

CREATE SEQUENCE public.event_log_sequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.event_log_sequence OWNER TO fix_api_trading;

--
-- Name: event_log; Type: TABLE; Schema: public; Owner: fix_api_trading
--

CREATE TABLE public.event_log (
    id integer DEFAULT nextval('public.event_log_sequence'::regclass) NOT NULL,
    "time" timestamp without time zone NOT NULL,
    beginstring character(8),
    sendercompid character varying(64),
    targetcompid character varying(64),
    session_qualifier character varying(64),
    text text NOT NULL
);


ALTER TABLE public.event_log OWNER TO fix_api_trading;

--
-- Name: flyway_schema_history; Type: TABLE; Schema: public; Owner: fix_api_trading
--

CREATE TABLE public.flyway_schema_history (
    installed_rank integer NOT NULL,
    version character varying(50),
    description character varying(200) NOT NULL,
    type character varying(20) NOT NULL,
    script character varying(1000) NOT NULL,
    checksum integer,
    installed_by character varying(100) NOT NULL,
    installed_on timestamp without time zone DEFAULT now() NOT NULL,
    execution_time integer NOT NULL,
    success boolean NOT NULL
);


ALTER TABLE public.flyway_schema_history OWNER TO fix_api_trading;

--
-- Name: messages; Type: TABLE; Schema: public; Owner: fix_api_trading
--

CREATE TABLE public.messages (
    beginstring character(8) NOT NULL,
    sendercompid character varying(64) NOT NULL,
    targetcompid character varying(64) NOT NULL,
    session_qualifier character varying(64) NOT NULL,
    msgseqnum integer NOT NULL,
    message text NOT NULL
);


ALTER TABLE public.messages OWNER TO fix_api_trading;

--
-- Name: messages_log_sequence; Type: SEQUENCE; Schema: public; Owner: fix_api_trading
--

CREATE SEQUENCE public.messages_log_sequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.messages_log_sequence OWNER TO fix_api_trading;

--
-- Name: messages_log; Type: TABLE; Schema: public; Owner: fix_api_trading
--

CREATE TABLE public.messages_log (
    id integer DEFAULT nextval('public.messages_log_sequence'::regclass) NOT NULL,
    "time" timestamp without time zone NOT NULL,
    beginstring character(8),
    sendercompid character varying(64),
    targetcompid character varying(64),
    session_qualifier character varying(64),
    text text NOT NULL
);


ALTER TABLE public.messages_log OWNER TO fix_api_trading;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: fix_api_trading
--

CREATE TABLE public.sessions (
    beginstring character(8) NOT NULL,
    sendercompid character varying(64) NOT NULL,
    targetcompid character varying(64) NOT NULL,
    session_qualifier character varying(64) NOT NULL,
    creation_time timestamp without time zone NOT NULL,
    incoming_seqnum integer NOT NULL,
    outgoing_seqnum integer NOT NULL
);


ALTER TABLE public.sessions OWNER TO fix_api_trading;

--
-- Name: event_backup_log event_backup_log_pkey; Type: CONSTRAINT; Schema: public; Owner: fix_api_trading
--

ALTER TABLE ONLY public.event_backup_log
    ADD CONSTRAINT event_backup_log_pkey PRIMARY KEY (id);


--
-- Name: event_log event_log_pkey; Type: CONSTRAINT; Schema: public; Owner: fix_api_trading
--

ALTER TABLE ONLY public.event_log
    ADD CONSTRAINT event_log_pkey PRIMARY KEY (id);


--
-- Name: flyway_schema_history flyway_schema_history_pk; Type: CONSTRAINT; Schema: public; Owner: fix_api_trading
--

ALTER TABLE ONLY public.flyway_schema_history
    ADD CONSTRAINT flyway_schema_history_pk PRIMARY KEY (installed_rank);


--
-- Name: messages_log messages_log_pkey; Type: CONSTRAINT; Schema: public; Owner: fix_api_trading
--

ALTER TABLE ONLY public.messages_log
    ADD CONSTRAINT messages_log_pkey PRIMARY KEY (id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: public; Owner: fix_api_trading
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (beginstring, sendercompid, targetcompid, session_qualifier, msgseqnum);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: fix_api_trading
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (beginstring, sendercompid, targetcompid, session_qualifier);


--
-- Name: flyway_schema_history_s_idx; Type: INDEX; Schema: public; Owner: fix_api_trading
--

CREATE INDEX flyway_schema_history_s_idx ON public.flyway_schema_history USING btree (success);


--
-- PostgreSQL database dump complete
--


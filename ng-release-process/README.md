
## Wyden.io Release Process

This document describes the current release process for Wyden.io software, merging the latest guidelines, practical feedback from recent Release Masters, and improvements based on real-world experience. The process is designed to ensure clarity, accountability, and smooth coordination across all involved teams.

### **Overview**

- **Release Frequency:** After each Sprint
- **Key Roles:** Release Manager (DEV, rotational), QA Release Manager (rotational), Team Leaders, CTO, Head of Backend Development
- **Environments:** QA, UAT, Demo, Garanti-UAT, BPCE-DEV


## **Release Workflow**

```mermaid
flowchart TD
    A[Kickoff: Start of Sprint] --> B["Monitor sprint goals execution"]
    B --> C["1st checkpoint - decide if code freeze can be announced"]
    C --> D[Devs focus on finishing mandatory release tasks, other tasks cannot be merged]
    D --> E[QA Deployment of most recent version from main branch]
    E --> F{QA Green Light, i.e. Release Signoff?}
    F -- No --> D
    F -- Yes --> G[UAT Upgrade]
    G --> H{Perform smoke tests on UAT}
    H -- No --> D
    H -- Yes --> I[Release Artifacts]
    I --> J[Upgrade Demo / Client envs]
    J --> K[Post-release Activities]
```


## **Step-by-Step Process**

### **1. Kickoff \& Preparation**

- Process starts with the sprint
- Assign Release Manager (DEV) and QA Release Manager.
- Schedule two release checkpoints with Team Leaders, CTO, and Head of Backend Development for alignment and risk assessment.
- Rename dedicated release channel to a name of current reelase


### **2. Code Freeze**

- Perform 1st checkpoint - decide if code freeze can be announced
- Announce code freeze; only bug fixes and stabilization allowed.
- Communicate freeze to both BE and FE teams on dedicated Release channel
- Non essential items not ready by freeze are moved to the next release unless otherwise agreed.

### **3. QA Environment Upgrade \& Testing**

- Update QA images and chart versions via pipeline.
- Build QA environment, typically using `dirty` mode (reloads only modified services).
- Run E2E regression tests and check Allure reports for results.
- Perform manual and exploratory QA testing on new features and bug fixes.
- Gather QA feedback as Jira tickets; critical issues are fixed immediately, and the process loops back if necessary[^2].


#### **2nd checkpoint**

- Around release date a 2nd checkpoint is done to see if release date can be kept, or maybe some of the scope should be dropped
- From that point the Release Manager and DEVS monitors the remaining issues very closely and pushing for the release is a highest prio
- A positive approach is required to try to push for a successful release, possibly acquiring some technical debt to be solved in future sprints



### **4. Versioning \& Merging**

- Bump `appVersion` in `Chart.yaml` (e.g., `2025.5.0`).
- Ask DevOps to merge `develop` to `main` for charts; ensure all quickfixes are included.
- Tags for services are created automatically or manually as needed.

### **5. Release signoff**

Release sign-off preconditions:
- [ ] All automated E2E tests passed on the RC build
- [ ] All items that require manual testing were checked and passed
- [ ] Deployments conducted on DEV / QA / UAT environments and no data migration errors or upgrade issues were found


### **5. UAT Environment Upgrade \& Acceptance**

- Upgrade UAT; verify all services and connectors are updated.
- Perform sanity checks and smoke tests - UAT deployement is a dry run of PROD updates
- If problems with UAT updates are found - identify them, fix, rollback UAT and redeploy again

### **6. Release Artifacts**

- If UAT is ok, then release artifacts


### **7. Deploy to Client Environments**

- **Demo:** Coordinate with Product and Presales; sanity check post-deployment.
- **Client UAT envs:** Coordinate with respective Project Manager

### **8. Post-release**

- Announce release completion.
- Document outcomes and lessons learned.
- Update release documentation as needed.


## **Roles \& Responsibilities**

| Role | Responsibilities |
| :-- | :-- |
| **Release Manager** | Deployments, support QA, clarify issues, assign/follow up on bugs, announce code freeze, coordinate release chat, report high-level ticket status per sprint goal, schedule checkpoints. |
| **QA Release Manager** | Feature re-testing, smoke/exploratory testing, acceptance tests, upgrade procedure testing, sign-off, support DEV during deployments. |
| **Team Leaders/CTO/Head of Backend** | Attend checkpoints, provide input on risks, approve critical release decisions. |

## **Special Notes \& Improvements**

- **Proactivity Required:** The Release Manager role demands a proactive approach, especially in tracking ticket status and coordinating between teams.
- **Feature Exclusion:** Features in beta are not included in releases or UAT by default.
- **Feedback Loop:** Both QA and business acceptance can trigger a return to previous steps if blockers are found.

**This document should be reviewed after each release cycle to ensure it remains accurate and reflects current practices.**



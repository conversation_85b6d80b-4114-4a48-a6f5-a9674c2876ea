stages:
  - version
  - build
  - publish
  - init
  - prepare
  - deploy
  - actor
  - test
  - allure
  - cleanup

default:
  retry:
    max: 2
    when: runner_system_failure

variables:
  RUN_TESTS:
    value: "runner"
    options:
      - "runner"
      - "dev"
      - "qa"
      - "qa_runner"
    description: "Choose the environment used for tests. (Default: runner)"
  PERF_TEST_PARAM:
    description: "Parameters to be passed to the performance tests. It also means that performance tests to be executed"
    value: ""
  SELECTED_TEST:
    description: "Select specific tests to be exexuted or leave black to perform RegressionSuite"
    value: ""
  DB_SET: "keycloak_runner accessgw_runner audit_runner booking_runner booking_metrics_runner booking_pnl_runner booking_reporting_runner booking_snapshot_runner booking_wal_runner order_history_runner ui_server_runner reference_data_runner fix_api_trading_runner fix_api_market_data_runner fix_api_drop_copy_runner fix_api_custom_ohlc_runner settlement_runner"

.cleanup_kubernetes_resources: &cleanup_kubernetes_resources |
  helm uninstall --wait -n wyden-runner wyden --ignore-not-found
  helm uninstall --wait -n wyden-runner keycloak --ignore-not-found
  helm uninstall --wait -n wyden-runner rabbitmq --ignore-not-found
  helm uninstall --wait -n wyden-runner vault-runner --ignore-not-found
  helm uninstall --wait -n wyden-runner fix-actor --ignore-not-found
  helm uninstall --wait -n wyden-runner wyden-mock --ignore-not-found
  helm uninstall --wait -n wyden-runner wyden-license-server-mock --ignore-not-found
  kubectl delete pods --all --namespace wyden-runner --grace-period=0 --force
  kubectl delete deployments --all --namespace wyden-runner
  kubectl delete --all pvc --namespace wyden-runner

.cleanup_database: &cleanup_database |
  ##DB_SET locaed in pipeline variables on top
  for db in $DB_SET; do
    PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U $POSTGRES_USER -W -d postgres -c "DROP DATABASE IF EXISTS $db WITH (FORCE);"
  done

.setup_database: &setup_database |
  ##DB_SET locaed in pipeline variables on top
  for db in $DB_SET; do
    PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U $POSTGRES_USER -W -d postgres -c "CREATE DATABASE $db;"
  done

.test_template: &test_template
  image: maven:3.8.4-eclipse-temurin-17
  stage: test
  allow_failure: true
  before_script:
    - apt-get update
    - apt-get install -y libglib2.0-0 libnss3 libnspr4 libdbus-1-3 libatk1.0-0 libatspi2.0-0 libxcomposite1 libxdamage1 libxfixes3 libxrandr2 libgbm1 libxkbcommon0 libasound2
  script:
    - echo "TEST_JOB_URL=$CI_JOB_URL" > build.env
    - echo "TEST_JOB_ID=$CI_JOB_ID" >> build.env
    - |
      if [[ ! -z "$SELECTED_TEST" ]]; then
        echo "Running selected tests: $SELECTED_TEST"
        ./gradlew test --tests="$SELECTED_TEST" -i
      elif [[ ! -z "$PERF_TEST_PARAM" ]]; then
        echo "Running performance tests with param: $PERF_TEST_PARAM"
        ./gradlew performanceTest $PERF_TEST_PARAM
      else
        echo "Running Regression tests"
        ./gradlew test --tests=RegressionSuite -i
      fi
  dependencies:
    - build
  tags:
    - kubernetes-heavy
  artifacts:
    when: always
    reports:
      junit:
        - build/test-results/**/TEST-*.xml
      dotenv:
        - build.env
    paths:
      - build/allure-results
      - build/test-results
      - build/log/scenario-runner.log
    expire_in: 7 days

.allure_template: &allure_template
  image: maven:3.8.4-eclipse-temurin-17
  stage: allure
  script:
    - PAGES_URL=$(echo "$CI_PAGES_URL" | sed 's|wyden.io/|wyden.io/-/|')
    - >
      echo '{
        "name": "Gitlab",
        "type": "gitlab",
        "buildOrder": '"$TEST_JOB_ID"',
        "buildUrl": "'"$TEST_JOB_URL"'",
        "buildName": "test-'"$ENVIRONMENT"'-environment",
        "reportUrl": "'"$PAGES_URL"'/-/jobs/'"$CI_JOB_ID"'/artifacts/public/index.html"
      }' > build/allure-results/executor.json
    - apt update && apt install -y unzip jq
    - |
      ENVIRONMENT_ID=$(curl -s --header "Authorization: Bearer ${GITLAB_CI_ACCESS_TOKEN}" "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/environments?name=Allure%20tests%20for%20${ENVIRONMENT}" | jq ".[0].id")
      JOB_ID=$(curl -s --header "Authorization: Bearer ${GITLAB_CI_ACCESS_TOKEN}" "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/environments/${ENVIRONMENT_ID}" | jq -r ".last_deployment.deployable.id")
    - |
      curl -s --output report.zip --header "Authorization: Bearer ${GITLAB_CI_ACCESS_TOKEN}" "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/jobs/${JOB_ID}/artifacts"
    - mkdir -p build/allure-results/history
    - unzip -j -o report.zip 'public/history/*' -d build/allure-results/history || true
    - echo environment="$ENVIRONMENT" >> build/allure-results/environment.properties
    - ./gradlew allureReport
    - mv build/reports/allure-report/allureReport public/
  artifacts:
    when: always
    paths:
      - public
    expire_in: 7 days

version-check:
  stage: version
  allow_failure: true
  script:
    - echo "Version not updated. Please update version.properties file"
    - exit 1
  tags:
    - kubernetes
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - version.properties
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - src/main/java/io/wyden/test/scenariorunner/integration/*
      when: always
    - when: never

build:
  stage: build
  image: maven:3.8.4-eclipse-temurin-17
  script:
    - ./gradlew testClasses performanceTestClasses assemble
  artifacts:
    paths:
      - build/libs/
    expire_in: 1 days
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      when: always
      allow_failure: false
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      when: always
    - when: never

publish-lib:
  stage: publish
  image: maven:3.8.4-eclipse-temurin-17
  script:
    - ./gradlew publish
  dependencies:
    - build
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
      when: on_success
    - when: never

cleanup-kubernetes(init):
  stage: init
  image: $HELM_KUBECTL_IMAGE
  script:
    - *cleanup_kubernetes_resources
  resource_group: runner
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never

cleanup-database(init):
  stage: init
  image: postgres
  resource_group: runner
  script:
    - *cleanup_database
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never

prepare-database:
  image: postgres
  stage: prepare
  resource_group: runner
  script:
    - *setup_database
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never

prepare-keycloak:
  image: $HELM_KUBECTL_IMAGE
  stage: prepare
  resource_group: runner
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_DEPTH: 1
    GIT_SUBMODULE_UPDATE_FLAGS: --remote
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade -i -n wyden-runner keycloak bitnami/keycloak --version $KEYCLOAK_CHART_VERSION --wait --timeout 30m -f .k8s/infra/keycloak/values-runner-aws.yaml
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never
  needs:
    - job: prepare-database

prepare-rabbit:
  image: $HELM_KUBECTL_IMAGE
  stage: prepare
  resource_group: runner
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_DEPTH: 1
    GIT_SUBMODULE_UPDATE_FLAGS: --remote
  script:
    - helm repo add bitnami https://charts.bitnami.com/bitnami
    - helm repo update
    - helm upgrade -i -n wyden-runner rabbitmq bitnami/rabbitmq --version 12.15.0 --wait --timeout 20m -f .k8s/infra/rabbitmq/values-runner-aws.yaml --set-file extraSecrets.load-definition."load_definition\.json"=.k8s/infra/rabbitmq/rabbit.definitions.json
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never

prepare-vault:
  image: $HELM_KUBECTL_IMAGE
  stage: prepare
  resource_group: runner
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_DEPTH: 1
    GIT_SUBMODULE_UPDATE_FLAGS: --remote
  script:
    - helm repo add hashicorp https://helm.releases.hashicorp.com
    - helm repo update
    - helm secrets upgrade -i -n wyden-runner vault-runner hashicorp/vault --version 0.25.0 --wait --timeout 20m -f .k8s/infra/vault/vault/values-runner-aws.yaml
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never

deploy-app:
  image: $HELM_KUBECTL_IMAGE
  stage: deploy
  resource_group: runner
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_DEPTH: 1
    GIT_SUBMODULE_UPDATE_FLAGS: --remote
  script:
    - helm repo add --username $NEXUS_USER --password $NEXUS_PASSWORD wyden https://repo.wyden.io/nexus/repository/wyden-dev/
    - helm repo update
    - helm pull wyden/wyden --untar
    - if [ "$RUN_TESTS" = "qa_runner" ]; then
      echo "Install runner env with qa repo service versions";
      git clone https://oauth2:${GITLAB_CI_ACCESS_TOKEN}@${GITLAB_HOSTNAME}/atcloud/qa --branch main;
      helm secrets upgrade --wait --install -n wyden-runner wyden ./wyden -f ./wyden/secret.runner-aws.yaml -f ./wyden/values-runner-aws.yaml -f ./qa/images.yaml -f ./qa/qa_runner_images.yaml $DEPLOY_APP_OPTION --timeout 20m;
      else
      echo "Install runner env with latest service versions";
      helm secrets upgrade --wait --install -n wyden-runner wyden ./wyden -f ./wyden/secret.runner-aws.yaml -f ./wyden/values-runner-aws.yaml $DEPLOY_APP_OPTION --timeout 20m;
      fi
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: on_success
    - when: never

prepare-mock:
  image: $HELM_KUBECTL_IMAGE
  stage: deploy
  resource_group: runner
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_DEPTH: 1
    GIT_SUBMODULE_UPDATE_FLAGS: --remote
  script:
    - helm repo add --username $NEXUS_USER --password $NEXUS_PASSWORD wyden https://repo.wyden.io/nexus/repository/wyden-dev/
    - helm repo update
    - helm pull wyden/wyden-mock --untar
    - helm upgrade --install wyden-mock ./wyden-mock --namespace wyden-runner --wait --create-namespace --timeout 20m -f ./wyden-mock/values-runner-aws.yaml
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: on_success
    - when: never

prepare-license-server-mock:
  image: $HELM_KUBECTL_IMAGE
  stage: deploy
  resource_group: runner
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_DEPTH: 1
    GIT_SUBMODULE_UPDATE_FLAGS: --remote
  script:
    - helm repo add --username $NEXUS_USER --password $NEXUS_PASSWORD wyden https://repo.wyden.io/nexus/repository/wyden-dev/
    - helm repo update
    - helm pull wyden/wyden-license-server-mock --untar
    - helm upgrade --install wyden-license-server-mock ./wyden-license-server-mock --namespace wyden-runner --wait --create-namespace --timeout 20m -f ./wyden-license-server-mock/values-runner-aws.yaml
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: on_success
    - when: never

prepare-fix-actor:
  image: $HELM_KUBECTL_IMAGE
  stage: actor
  resource_group: runner
  variables:
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_DEPTH: 1
    GIT_SUBMODULE_UPDATE_FLAGS: --remote
  script:
    - helm repo add --username $NEXUS_USER --password $NEXUS_PASSWORD wyden https://repo.wyden.io/nexus/repository/wyden-dev/
    - helm repo update
    - helm pull wyden/wyden-fix-actor --untar
    - helm upgrade --install fix-actor ./wyden-fix-actor --namespace wyden-runner --wait --create-namespace --timeout 20m -f ./wyden-fix-actor/values-runner-aws.yaml $DEPLOY_FIX_ACTOR_OPTION
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: on_success
    - when: never

test-runner-environment:
  <<: *test_template
  resource_group: runner
  variables:
    ENVIRONMENT: runner
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "runner"
      when: on_success
    - when: never

allure-runner-environment:
  <<: *allure_template
  variables:
    ENVIRONMENT: runner
    SELECTED_TEST: BetaRegressionSuite
  environment:
    name: "Allure tests for runner"
    url: "https://atcloud.pages.wyden.io/-/oems/integration-tests/scenario-runner/-/jobs/$CI_JOB_ID/artifacts/public/index.html"
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "runner" && $PERF_TEST_PARAM == ""
      when: on_success
    - when: never
  needs:
    - job: test-runner-environment
      artifacts: true

test-dev-environment:
  <<: *test_template
  variables:
    ENVIRONMENT: dev
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "dev"
      when: always
    - when: never

allure-dev-environment:
  <<: *allure_template
  variables:
    ENVIRONMENT: dev
  environment:
    name: "Allure tests for dev"
    url: "https://atcloud.pages.wyden.io/-/oems/integration-tests/scenario-runner/-/jobs/$CI_JOB_ID/artifacts/public/index.html"
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "dev" && $PERF_TEST_PARAM == ""
      when: always
    - when: never
  needs:
    - job: test-dev-environment
      artifacts: true

test-qa-environment:
  <<: *test_template
  variables:
    ENVIRONMENT: qa
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "qa"
      when: always
    - when: never

allure-qa-environment:
  <<: *allure_template
  environment:
    name: "Allure tests for qa"
    url: "https://atcloud.pages.wyden.io/-/oems/integration-tests/scenario-runner/-/jobs/$CI_JOB_ID/artifacts/public/index.html"
  variables:
    ENVIRONMENT: qa
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "qa" && $PERF_TEST_PARAM == ""
      when: always
    - when: never
  needs:
    - job: test-qa-environment
      artifacts: true

test-qa-runner-environment:
  <<: *test_template
  variables:
    ENVIRONMENT: runner
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "qa_runner"
      when: always
    - when: never

allure-qa-runner-environment:
  <<: *allure_template
  environment:
    name: "Allure report for qa tagged versions on runner env"
    url: "https://atcloud.pages.wyden.io/-/oems/integration-tests/scenario-runner/-/jobs/$CI_JOB_ID/artifacts/public/index.html"
  variables:
    ENVIRONMENT: runner
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS == "qa_runner" && $PERF_TEST_PARAM == ""
      when: always
    - when: never
  needs:
    - job: test-qa-runner-environment
      artifacts: true

cleanup-kubernetes(post):
  image: $HELM_KUBECTL_IMAGE
  stage: cleanup
  resource_group: runner
  script:
    - *cleanup_kubernetes_resources
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never

cleanup-database(post):
  image: postgres
  stage: cleanup
  resource_group: runner
  script:
    - *cleanup_database
  rules:
    - if: $CI_PIPELINE_SOURCE =~ /^(web|schedule|pipeline)$/ && $RUN_TESTS =~ /.*runner.*/
      when: always
    - when: never

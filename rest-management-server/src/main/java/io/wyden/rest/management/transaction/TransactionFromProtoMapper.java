package io.wyden.rest.management.transaction;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.DepositSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeSnapshot;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.SettlementSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WithdrawalSnapshot;
import io.wyden.rest.management.domain.TransactionModel;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static io.wyden.rest.management.common.ProtoMappingUtils.bd;
import static io.wyden.rest.management.common.ProtoMappingUtils.dt;
import static org.slf4j.LoggerFactory.getLogger;

public final class TransactionFromProtoMapper {

    private static final Logger LOGGER = getLogger(TransactionFromProtoMapper.class);

    private TransactionFromProtoMapper() {
    }

    @Nullable
    public static TransactionModel.Transaction map(TransactionSnapshot transaction) {

        if (transaction == null) {
            return null;
        }

        if (transaction.hasClientCashTrade()) {
            ClientCashTradeSnapshot snapshot = transaction.getClientCashTrade();

            return new TransactionModel.ClientCashTrade(
                snapshot.getUuid(),
                snapshot.getReservationRef(),
                dt(snapshot.getDateTime()),
                snapshot.getOrderId(),
                snapshot.getParentOrderId(),
                snapshot.getRootOrderId(),
                snapshot.getClientRootOrderId(),
                snapshot.getExtOrderId(),
                snapshot.getExecutionId(),
                snapshot.getVenueExecutionId(),
                snapshot.getUnderlyingExecutionId(),
                snapshot.getRootExecutionId(),
                TransactionModel.TransactionType.CLIENT_CASH_TRADE,
                snapshot.getSettled(),
                dt(snapshot.getSettledDateTime()),
                snapshot.getSettlementId(),
                snapshot.getClientSettlementId(),
                snapshot.getBaseCurrency(),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                bd(snapshot.getLeavesQuantity()),
                bd(snapshot.getPrice()),
                snapshot.getPortfolio(),
                snapshot.getCounterPortfolio(),
                snapshot.getDescription(),
                map(snapshot.getTransactionFeeList())
            );
        }

        if (transaction.hasStreetCashTrade()) {
            StreetCashTradeSnapshot snapshot = transaction.getStreetCashTrade();

            return new TransactionModel.StreetCashTrade(
                snapshot.getUuid(),
                snapshot.getReservationRef(),
                dt(snapshot.getDateTime()),
                snapshot.getOrderId(),
                snapshot.getParentOrderId(),
                snapshot.getRootOrderId(),
                snapshot.getClientRootOrderId(),
                snapshot.getExtOrderId(),
                snapshot.getExecutionId(),
                snapshot.getVenueExecutionId(),
                snapshot.getUnderlyingExecutionId(),
                snapshot.getRootExecutionId(),
                TransactionModel.TransactionType.STREET_CASH_TRADE,
                snapshot.getSettled(),
                dt(snapshot.getSettledDateTime()),
                snapshot.getSettlementId(),
                snapshot.getClientSettlementId(),
                snapshot.getBaseCurrency(),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                bd(snapshot.getLeavesQuantity()),
                bd(snapshot.getPrice()),
                snapshot.getPortfolio(),
                snapshot.getVenueAccount(),
                snapshot.getDescription(),
                map(snapshot.getTransactionFeeList())
            );
        }

        if (transaction.hasDeposit()) {
            DepositSnapshot snapshot = transaction.getDeposit();

            return new TransactionModel.Deposit(
                snapshot.getUuid(),
                snapshot.getReservationRef(),
                dt(snapshot.getDateTime()),
                snapshot.getExecutionId(),
                snapshot.getVenueExecutionId(),
                TransactionModel.TransactionType.DEPOSIT,
                snapshot.getSettled(),
                dt(snapshot.getSettledDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getPortfolio(),
                snapshot.getAccount(),
                snapshot.getFeePortfolioId(),
                snapshot.getFeeAccountId(),
                snapshot.getDescription(),
                map(snapshot.getTransactionFeeList())
            );
        }

        if (transaction.hasWithdrawal()) {
            WithdrawalSnapshot snapshot = transaction.getWithdrawal();

            return new TransactionModel.Withdrawal(
                snapshot.getUuid(),
                snapshot.getReservationRef(),
                dt(snapshot.getDateTime()),
                snapshot.getExecutionId(),
                snapshot.getVenueExecutionId(),
                TransactionModel.TransactionType.WITHDRAWAL,
                snapshot.getSettled(),
                dt(snapshot.getSettledDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getPortfolio(),
                snapshot.getAccount(),
                snapshot.getFeePortfolioId(),
                snapshot.getFeeAccountId(),
                snapshot.getDescription(),
                map(snapshot.getTransactionFeeList())
            );
        }

        if (transaction.hasPortfolioCashTransfer()) {
            PortfolioCashTransferSnapshot snapshot = transaction.getPortfolioCashTransfer();

            return new TransactionModel.PortfolioCashTransfer(
                snapshot.getUuid(),
                snapshot.getReservationRef(),
                dt(snapshot.getDateTime()),
                snapshot.getExecutionId(),
                snapshot.getVenueExecutionId(),
                TransactionModel.TransactionType.PORTFOLIO_CASH_TRANSFER,
                snapshot.getSettled(),
                dt(snapshot.getSettledDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getDescription(),
                snapshot.getFromPortfolioId(),
                snapshot.getToPortfolioId(),
                map(snapshot.getTransactionFeeList())
            );
        }

        if (transaction.hasAccountCashTransfer()) {
            AccountCashTransferSnapshot snapshot = transaction.getAccountCashTransfer();

            return new TransactionModel.AccountCashTransfer(
                snapshot.getUuid(),
                snapshot.getReservationRef(),
                dt(snapshot.getDateTime()),
                snapshot.getExecutionId(),
                snapshot.getVenueExecutionId(),
                TransactionModel.TransactionType.ACCOUNT_CASH_TRANSFER,
                snapshot.getSettled(),
                dt(snapshot.getSettledDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getDescription(),
                snapshot.getFromAccountId(),
                snapshot.getToAccountId(),
                snapshot.getFeePortfolioId(),
                snapshot.getFeeAccountId(),
                map(snapshot.getTransactionFeeList())
            );
        }

        if (transaction.hasSettlement()) {
            SettlementSnapshot snapshot = transaction.getSettlement();

            return new TransactionModel.Settlement(
                snapshot.getUuid(),
                snapshot.getClientSettlementId(),
                dt(snapshot.getDateTime()),
                dt(snapshot.getDateTime()),
                snapshot.getDescription(),
                TransactionModel.TransactionType.SETTLEMENT,
                snapshot.getSettledTransactionIdsList()
            );
        }

        if (transaction.hasFee()) {
            FeeSnapshot snapshot = transaction.getFee();

            return new TransactionModel.Fee(
                snapshot.getUuid(),
                snapshot.getReservationRef(),
                dt(snapshot.getDateTime()),
                snapshot.getOrderId(),
                snapshot.getParentOrderId(),
                snapshot.getRootOrderId(),
                snapshot.getExecutionId(),
                snapshot.getVenueExecutionId(),
                snapshot.getUnderlyingExecutionId(),
                snapshot.getRootExecutionId(),
                TransactionModel.TransactionType.FEE,
                snapshot.getSettled(),
                dt(snapshot.getSettledDateTime()),
                snapshot.getCurrency(),
                bd(snapshot.getQuantity()),
                snapshot.getPortfolioId(),
                snapshot.getAccountId(),
                snapshot.getDescription()
            );
        }

        LOGGER.error("Unsupported transaction type. Transaction will be skipped: ({})", transaction);
        return null;
    }

    private static Collection<TransactionModel.TransactionFee> map(List<Fee> fees) {
        if (fees == null) {
            return null;
        }

        return fees.stream()
            .map(fee -> new TransactionModel.TransactionFee(
                bd(fee.getAmount()),
                fee.getCurrency(),
                map(fee.getFeeType())))
            .toList();
    }

    public static TransactionModel.FeeType map(FeeType feeType) {
        if (feeType == null) {
            return null;
        }

        return switch (feeType) {
            case EXCHANGE_FEE -> TransactionModel.FeeType.EXCHANGE_FEE;
            case TRANSACTION_FEE -> TransactionModel.FeeType.TRANSACTION_FEE;
            case FIXED_FEE -> TransactionModel.FeeType.FIXED_FEE;
            default -> TransactionModel.FeeType.UNSPECIFIED;
        };
    }

    public static long isoUtcTimeToLong(@NotNull String isoString) {
        return Optional.of(isoString)
            .map(DateUtils::isoUtcTimeToEpochMillis)
            .map(Long::parseLong)
            .orElse(0L);
    }
}

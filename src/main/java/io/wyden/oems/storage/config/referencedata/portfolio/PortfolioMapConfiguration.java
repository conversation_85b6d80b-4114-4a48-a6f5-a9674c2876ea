package io.wyden.oems.storage.config.referencedata.portfolio;

import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.config.StoredMapConfigBean;
import io.wyden.oems.storage.web.StringProtobufMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static io.wyden.oems.storage.config.MapStores.defaultEagerStoreConfig;

@Configuration
public class PortfolioMapConfiguration {

    @Bean
    HazelcastMapConfigBean portfolioMapConfig(PortfolioMapStore mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new PortfolioMapConfig();
        MapStoreConfig mapStoreConfig = defaultEagerStoreConfig(mapStore, 0, true);
        return new StoredMapConfigBean(hazelcastMapConfig, mapStoreConfig);
    }

    @Bean
    WebAccess portfolioMapWebAccess(HazelcastInstance hazelcast, PortfolioMapStore mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new PortfolioMapConfig();
        return new StringProtobufMapWebAccess<>(hazelcast, hazelcastMapConfig, Portfolio.getDefaultInstance(), mapStore);
    }
}

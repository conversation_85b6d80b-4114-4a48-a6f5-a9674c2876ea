package io.wyden.booking.snapshotter.application.state;

import com.google.protobuf.Message;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.booking.snapshotter.application.reservation.ReservationFromProtoMapper;
import io.wyden.booking.snapshotter.application.transaction.TransactionFromProtoMapper;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.snapshotter.domain.ledgerentry.SimpleReference;
import io.wyden.booking.snapshotter.domain.position.Position;
import io.wyden.booking.snapshotter.domain.reservation.Reservation;
import io.wyden.booking.snapshotter.domain.state.ReservationReleaseStateInput;
import io.wyden.booking.snapshotter.domain.state.ReservationStateInput;
import io.wyden.booking.snapshotter.domain.state.StateManager;
import io.wyden.booking.snapshotter.domain.state.TradeStateInput;
import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.booking.ReservationReleaseRequest;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static io.wyden.booking.snapshotter.domain.ledgerentry.SimpleReference.accountReference;
import static io.wyden.booking.snapshotter.domain.ledgerentry.SimpleReference.portfolioReference;

/**
 * Responsible for providing state needed for Command processing.
 * All required entities will be fetched via StateManager (check in cache first, fallback to DB if not present, fallback to creating a new entity).
 */
@Component
public class StateProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(StateProvider.class);

    private final StateManager stateManager;
    private final Tracing tracing;

    public StateProvider(StateManager stateManager,
                         Tracing tracing) {
        this.stateManager = stateManager;
        this.tracing = tracing;
    }

    public TradeStateInput fetchStateInput(TransactionSnapshot transactionSnapshot) {
        try (var ignored = tracing.createSpan(getSpanName(transactionSnapshot), SpanKind.INTERNAL)) {
            Transaction transaction = TransactionFromProtoMapper.map(transactionSnapshot);
            return fetchStateInput(transaction);
        }
    }

    public ReservationStateInput fetchStateInput(ReservationSnapshot reservationSnapshot) {
        try (var ignored = tracing.createSpan(getSpanName(reservationSnapshot), SpanKind.INTERNAL)) {
            Reservation reservation = ReservationFromProtoMapper.map(reservationSnapshot);
            return fetchStateInput(reservation);
        } catch (Exception e) {
            LOGGER.error("Failed to map ReservationSnapshot to Reservation", e);
            throw e;
        }
    }

    private TradeStateInput fetchStateInput(Transaction transaction) {
        LOGGER.trace("Fetching state input");
        Map<SimpleReference, Position> positions = new HashMap<>();

        Set<String> affectedInstruments = transaction.getInstrumentsAndFeeCurrencies();

        String reservationRef = transaction.getReservationRef();
        Optional<Reservation> optionalReservation = stateManager.findReservationByReservationRef(reservationRef);
        optionalReservation.ifPresent(reservation -> affectedInstruments.addAll(reservation.getInstrumentsAndFeeCurrencies()));
        Reservation reservation = optionalReservation
            .orElse(null);

        affectedInstruments.forEach(instrument -> {
            transaction.getAffectedPortfolioIds()
                .forEach(portfolioId -> stateManager.findPositionByInstrumentAndPortfolioId(instrument, portfolioId)
                    .ifPresent(position -> positions.put(portfolioReference(instrument, position.getPortfolioId()), position)));

            transaction.getAffectedAccountIds()
                .forEach(accountId -> stateManager.findPositionByInstrumentAndAccountId(instrument, accountId)
                    .ifPresent(position -> positions.put(accountReference(position.getInstrument(), position.getAccountId()), position)));
        });

        return new TradeStateInput(positions, transaction, reservation);
    }

    private ReservationStateInput fetchStateInput(Reservation reservation) {
        Map<SimpleReference, Position> positions = new HashMap<>();

        reservation.getInstrumentsAndFeeCurrencies().forEach(instrument -> {
            reservation.getAffectedPortfolioIds()
                .forEach(portfolioId -> stateManager.findPositionByInstrumentAndPortfolioId(instrument, portfolioId)
                    .ifPresent(position -> positions.put(portfolioReference(instrument, position.getPortfolioId()), position)));

            reservation.getAffectedAccountIds()
                .forEach(accountId -> stateManager.findPositionByInstrumentAndAccountId(instrument, accountId)
                    .ifPresent(position -> positions.put(accountReference(position.getInstrument(), position.getAccountId()), position)));
        });

        return new ReservationStateInput(positions, reservation);
    }

    public ReservationReleaseStateInput fetchStateInput(ReservationReleaseRequest command) {
        try (var ignored = tracing.createSpan(getSpanName(command), SpanKind.INTERNAL)) {
            String reservationReference = command.getReservationReference();
            Optional<Reservation> byReservationRef = stateManager.findReservationByReservationRef(reservationReference);
            Map<Reservation, List<LedgerEntry>> reservationLedgerEntries;
            if (byReservationRef.isPresent()) {
                Reservation reservation = byReservationRef.get();
                List<LedgerEntry> entries = new ArrayList<>(stateManager.findLedgerEntriesByReservationRef(reservationReference));
                reservationLedgerEntries = Map.of(reservation, entries);
                Map<SimpleReference, Position> affectedPositions = findAffectedPositions(reservation);
                return new ReservationReleaseStateInput(affectedPositions, reservation, reservationLedgerEntries);
            } else {
                throw new IllegalStateException("No reservation found for reference: " + reservationReference);
            }
        }
    }

    private Map<SimpleReference, Position> findAffectedPositions(Reservation reservation) {
        Map<SimpleReference, Position> positions = new HashMap<>();
        reservation.getInstrumentsAndFeeCurrencies().forEach(instrument -> {
            reservation.getAffectedPortfolioIds()
                .forEach(portfolioId -> stateManager.findPositionByInstrumentAndPortfolioId(instrument, portfolioId)
                    .ifPresent(position -> positions.put(new SimpleReference(instrument, position.getPortfolioId(), position.getAccountId()), position)));

            reservation.getAffectedAccountIds()
                .forEach(accountId -> stateManager.findPositionByInstrumentAndAccountId(instrument, accountId)
                    .ifPresent(position -> positions.put(new SimpleReference(instrument, position.getPortfolioId(), position.getAccountId()), position)));
        });
        return positions;
    }

    private static <M extends Message> String getSpanName(M persistedCommand) {
        return "booking-snapshotter.command.processing.fetch.for" + persistedCommand.getDescriptorForType().getName();
    }
}

package io.wyden.booking.snapshotter.application.wal;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.booking.snapshotter.application.state.SequenceGapRecoveryService;
import io.wyden.booking.snapshotter.domain.state.tracking.SnapshotterTrackingRepositoryFacade;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WalEvent;
import io.wyden.published.oems.OemsExecType;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import static com.google.common.base.MoreObjects.firstNonNull;
import static io.wyden.booking.snapshotter.infrastructure.telemetry.Meters.BOOKING_SNAPSHOTTER_WAL_EVENT_INCOMING;
import static io.wyden.booking.snapshotter.infrastructure.telemetry.Meters.bookingCommandConsumerLatencyTimer;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class WalEventConsumer implements MessageConsumer<WalEvent> {

    private static final Logger LOGGER = getLogger(WalEventConsumer.class);

    private final WalEventProcessor walEventProcessor;
    private final SequenceGapRecoveryService gapRecoveryService;
    private final RabbitExchange<WalEvent> walEventExchange;
    private final String queueName;
    private final boolean shouldRequeueOnError;
    private final boolean shouldBreakOnError;
    private final SnapshotterTrackingRepositoryFacade snapshotRepository;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;
    private final MeterRegistry meterRegistry;
    private final Tracing tracing;

    private final RabbitQueue<WalEvent> queue;
    private long lastProcessedSeqNum = 0;

    public WalEventConsumer(WalEventProcessor walEventProcessor,
                            SequenceGapRecoveryService gapRecoveryService,
                            RabbitExchange<WalEvent> walEventExchange,
                            @Value("${queue.wal-event}") String queueName,
                            @Value("${wal.recovery.onstartup}") boolean isRecoveryRequired,
                            @Value("${wal.onerror.requeue}") boolean shouldRequeueOnError,
                            @Value("${wal.onerror.break}") boolean shouldBreakOnError,
                            SnapshotterTrackingRepositoryFacade snapshotRepository,
                            @Value("${spring.application.name}") String consumerName,
                            RabbitIntegrator rabbitIntegrator,
                            MeterRegistry meterRegistry,
                            Tracing tracing) {
        this.walEventProcessor = walEventProcessor;
        this.gapRecoveryService = gapRecoveryService;
        this.walEventExchange = walEventExchange;
        this.queueName = queueName;
        this.shouldRequeueOnError = shouldRequeueOnError;
        this.shouldBreakOnError = shouldBreakOnError;
        this.snapshotRepository = snapshotRepository;
        this.consumerName = consumerName;
        this.rabbitIntegrator = rabbitIntegrator;
        this.meterRegistry = meterRegistry;
        this.tracing = tracing;
        this.queue = declareQueue();

        if (isRecoveryRequired) {
            ensureNoMissingEvents();
        } else {
            LOGGER.info("Startup recovery not required, attaching queue consumer.");
            lastProcessedSeqNum = firstNonNull(snapshotRepository.getLastDurablyProcessedCommand(), 0L);
            attachConsumer(queue);
        }
    }

    /**
     * Recover all missing events from the WAL service on startup
     */
    private void ensureNoMissingEvents() {
        LOGGER.info("Starting startup recovery");
        Long lastPersistedSequenceNumber = snapshotRepository.getLastDurablyProcessedCommand();
        List<WalEvent> recoveredEvents = gapRecoveryService.recoverAllMissingEvents(firstNonNull(lastPersistedSequenceNumber, 0L));
        processRecoveredEvents(recoveredEvents);

        LOGGER.info("Startup recovery complete. Recovered {} events", recoveredEvents.size());
        attachConsumer(queue);
    }

    @Override
    public ConsumptionResult consume(WalEvent walEvent, AMQP.BasicProperties basicProperties) {
        LOGGER.info("Received WalEvent: {}", walEvent);

        updateMetrics(walEvent);

        try (var ignored = tracing.createSpan("booking-snapshotter.event.enqueue.consume." + walEvent.getEventCase().name(), SpanKind.CONSUMER)) {
            return recordLatencyIn(latencyTimer(walEvent)).of(() -> doConsume(walEvent));
        }
    }

    private ConsumptionResult doConsume(WalEvent walEvent) {
        long sequenceNumber = walEvent.getSequenceNumber();

        boolean shouldSkip = !ensureNoMissingEvents(sequenceNumber, lastProcessedSeqNum);
        if (shouldSkip) {
            return ConsumptionResult.consumed();
        }

        if (shouldSkipRestatedTransaction(walEvent)) {
            LOGGER.info("{} - Skipping RESTATED transaction", sequenceNumber);
            lastProcessedSeqNum = walEvent.getSequenceNumber();
            return ConsumptionResult.consumed();
        }

        LOGGER.info("{} - Processing realtime event.", sequenceNumber);

        // Process the current event
        if (walEventProcessor.process(walEvent)) {
            lastProcessedSeqNum = walEvent.getSequenceNumber();
            return ConsumptionResult.consumed();
        } else {
            if (shouldBreakOnError) {
                throw new RuntimeException("%d - Failed to process event - aborting".formatted(sequenceNumber));
            }
            return shouldRequeueOnError ? ConsumptionResult.failureNeedsRequeue() : ConsumptionResult.failureNonRecoverable();
        }
    }

    private boolean ensureNoMissingEvents(long inboundSequenceNumber, long lastProcessedSequence) {
        long expectedSequence = lastProcessedSequence + 1;
        if (inboundSequenceNumber != expectedSequence) {
            if (inboundSequenceNumber < expectedSequence) {
                LOGGER.warn("Received already processed sequence number: {}, current: {}. Skipping processing", inboundSequenceNumber, lastProcessedSequence);
                return false;
            }

            LOGGER.warn("Sequence gap detected. Expected: {}, Received: {}", expectedSequence, inboundSequenceNumber);
            List<WalEvent> gapRecovered = gapRecoveryService.recoverGap(expectedSequence, inboundSequenceNumber);
            LOGGER.info("Recovered {} events before realtime event", gapRecovered.size());
            processRecoveredEvents(gapRecovered);
        }

        return true;
    }

    private void processRecoveredEvents(List<WalEvent> recoveredEvents) {
        if (!recoveredEvents.isEmpty()) {
            LOGGER.info("Processing {} recovered events", recoveredEvents.size());
            int processedCount = 0;

            for (WalEvent event : recoveredEvents) {
                if (walEventProcessor.process(event)) {
                    lastProcessedSeqNum = event.getSequenceNumber();
                    processedCount++;
                } else {
                    LOGGER.error("Failed to process recovered event: {}", event);
                    if (shouldBreakOnError) {
                        throw new RuntimeException("Failed to process event %s. Aborting".formatted(event));
                    }
                    if (shouldRequeueOnError) {
                        break;
                    }
                }
            }

            LOGGER.info("Successfully processed {}/{} recovered events", processedCount, recoveredEvents.size());
        } else {
            LOGGER.info("No events to recover");
        }
    }

    private boolean shouldSkipRestatedTransaction(WalEvent walEvent) {
        if (walEvent.getEventCase() != WalEvent.EventCase.TRANSACTION) {
            return false;
        }

        TransactionSnapshot transaction = walEvent.getTransaction();

        switch (transaction.getTransactionCase()) {
            case CLIENT_CASH_TRADE -> {
                return transaction.getClientCashTrade().getExecType() == OemsExecType.RESTATED;
            }
            case STREET_CASH_TRADE -> {
                return transaction.getStreetCashTrade().getExecType() == OemsExecType.RESTATED;
            }
            case CLIENT_ASSET_TRADE -> {
                return transaction.getClientAssetTrade().getExecType() == OemsExecType.RESTATED;
            }
            case STREET_ASSET_TRADE -> {
                return transaction.getStreetAssetTrade().getExecType() == OemsExecType.RESTATED;
            }
            default -> {
                return false;
            }
        }
    }

    private void updateMetrics(WalEvent walEvent) {
        try {
            Tags tags = Tags.of(
                "eventType", walEvent.getEventCase().name(),
                "source", walEvent.getMetadata().getSource());
            meterRegistry.counter(BOOKING_SNAPSHOTTER_WAL_EVENT_INCOMING, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private Timer latencyTimer(WalEvent walEvent) {
        try {
            return bookingCommandConsumerLatencyTimer(meterRegistry, walEvent.getEventCase());
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }

    private RabbitQueue<WalEvent> declareQueue() {
        RabbitQueue<WalEvent> queue = new ExpiringRabbitQueueBuilder<WalEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .setMessageTTL(Duration.ofHours(12))
            .setSingleActiveConsumer(true)
            .declare();

        queue.bindWithHeaders(walEventExchange, MatchingCondition.ALL, Map.of());
        LOGGER.info("Binding exchange {} and queue {}", walEventExchange.getName(), queue.getName());
        return queue;
    }

    private String attachConsumer(RabbitQueue<WalEvent> queue) {
        return queue.attachConsumer(WalEvent.parser(), this);
    }

    public void resetSequenceNumber(long sequenceNumber) {
        lastProcessedSeqNum = sequenceNumber;
    }
}

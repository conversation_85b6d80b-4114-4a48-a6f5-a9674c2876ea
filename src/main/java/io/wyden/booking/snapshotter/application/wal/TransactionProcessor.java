package io.wyden.booking.snapshotter.application.wal;

import io.wyden.booking.snapshotter.application.state.StateProvider;
import io.wyden.booking.snapshotter.domain.BookingService;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.snapshotter.domain.position.Position;
import io.wyden.booking.snapshotter.domain.state.StateOutput;
import io.wyden.booking.snapshotter.domain.state.TradeStateInput;
import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.booking.snapshotter.domain.transaction.trade.Trade;
import io.wyden.published.booking.TransactionSnapshot;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;

import static io.wyden.cloudutils.tools.BigDecimalUtils.isNonZero;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class TransactionProcessor {

    private static final Logger LOGGER = getLogger(TransactionProcessor.class);

    private final StateProvider stateProvider;
    private final BookingService bookingService;

    public TransactionProcessor(StateProvider stateProvider,
                                BookingService bookingService) {
        this.stateProvider = stateProvider;
        this.bookingService = bookingService;
    }

    public StateOutput process(Long sequenceNumber, TransactionSnapshot transactionSnapshot) {
        if (!validate(transactionSnapshot)) {
            String reason = "Invalid TransactionSnapshot won't be booked: %s".formatted(transactionSnapshot);
            LOGGER.warn("{} - {}", reason, sequenceNumber);
            return StateOutput.failure(sequenceNumber, reason);
        }

        TradeStateInput stateInput = stateProvider.fetchStateInput(transactionSnapshot);
        Transaction transaction = stateInput.getTransaction();
        transaction.setSequenceNumber(sequenceNumber);
        LOGGER.debug("{} - Transaction parsed from snapshot: ({})", sequenceNumber, transaction);

        if (!validate(transaction)) {
            String reason = "Invalid Transaction won't be booked: %s".formatted(transaction);
            LOGGER.warn("{} - {}", reason, sequenceNumber);
            return StateOutput.failure(sequenceNumber, reason);
        }

        Collection<LedgerEntry> ledgerEntries = new ArrayList<>(transaction.getLedgerEntries());

        if (shouldReleaseRemaining(transaction)) {
            stateInput.getOptionalReservation()
                .ifPresent(reservation -> {
                    reservation.getRemainingEntries().stream()
                        .map(le -> le.setSequenceNumber(sequenceNumber))
                        .forEach(ledgerEntries::add);
                });
        }

        debugLedgerEntries(ledgerEntries);

        StateOutput stateOutput = bookingService.book(ledgerEntries, stateInput)
            .addSequenceNumber(sequenceNumber)
            .addTransaction(transaction)
            .buildSuccessful();

        debugPositions(stateOutput.processedPositions().values());

        return stateOutput;
    }

    private boolean validate(TransactionSnapshot transactionSnapshot) {
        if (transactionSnapshot.getTransactionCase() == TransactionSnapshot.TransactionCase.TRANSACTION_NOT_SET) {
            LOGGER.warn("TransactionSnapshot has TransactionCase TRANSACTION_NOT_SET, cannot produce Transaction from: {}", transactionSnapshot);
            return false;
        }
        return true;
    }

    private boolean validate(Transaction transaction) {
        if (transaction instanceof Trade && ((Trade) transaction).isRejectedOrCanceled()) {
            return true;
        }

        if (transaction instanceof Trade
            && isNonZero(((Trade) transaction).getQuantity())
            && ((Trade) transaction).getPrice() == null) {
            LOGGER.warn("Trade Transaction with quantity and no price is invalid: {}", transaction);
            return false;
        }

        return true;
    }

    public static void debugPositions(Collection<Position> positions) {
        if (!LOGGER.isDebugEnabled()) {
            return;
        }

        if (positions.isEmpty()) {
            LOGGER.debug("No positions affected by processing");
        } else {
            LOGGER.debug("Positions affected by processing (x{}):", positions.size());
            int count = 0;
            for (Position position : positions) {
                count++;
                LOGGER.debug("  [{}] PortfolioId: {}, AccountId: {}, Instrument: {}, Quantity: {}, Pending: {}, Trading: {}, Withdrawal: {}",
                    count,
                    position.getPortfolioId(),
                    position.getAccountId(),
                    position.getInstrument(),
                    position.getQuantity(),
                    position.getPendingQuantity(),
                    position.getAvailableForTradingQuantity(),
                    position.getAvailableForWithdrawalQuantity()
                );
            }
        }
    }

    public static void debugLedgerEntries(Collection<LedgerEntry> ledgerEntries) {
        if (!LOGGER.isDebugEnabled()) {
            return;
        }

        if (ledgerEntries.isEmpty()) {
            LOGGER.debug("No ledger entries parsed during processing");
        } else {
            LOGGER.debug("Ledger entries parsed during processing (x{}):", ledgerEntries.size());
            int count = 0;
            for (LedgerEntry entry : ledgerEntries) {
                count++;
                LOGGER.debug("  [{}]: {}", count, entry);
            }
        }
    }

    /**
     * Determines whether the transaction should release its associated reservations.
     * Reservations are released when a trade is either rejected, canceled, or closed.
     *
     * @param transaction The transaction to evaluate
     * @return true if the transaction should release its reservations, false otherwise
     */
    private boolean shouldReleaseRemaining(Transaction transaction) {
        if (transaction instanceof Trade trade) {
            return trade.isRejectedOrCanceled() || trade.isClosed();
        }

        return false;
    }
}

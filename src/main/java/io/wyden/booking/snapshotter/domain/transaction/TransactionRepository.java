package io.wyden.booking.snapshotter.domain.transaction;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TransactionRepository extends JpaRepository<Transaction, Long> {

//    long countByProperties(AuthorizedTransactionSearch request);
//
//    long countByProperties(AuthorizedTransactionSearch request, String after);

//    Collection<Transaction> findByProperties(AuthorizedTransactionSearch request);

//    void deleteByProperties(AuthorizedTransactionSearch request);

    Optional<Transaction> findByUuid(String transactionUuid);

    Optional<Transaction> findByExecutionId(String executionId);

//    Collection<Transaction> findByExecutionIdIn(Collection<String> executionIds);
}

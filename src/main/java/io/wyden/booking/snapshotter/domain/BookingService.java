package io.wyden.booking.snapshotter.domain;

import io.opentelemetry.api.trace.SpanKind;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntryReducer;
import io.wyden.booking.snapshotter.domain.position.Position;
import io.wyden.booking.snapshotter.domain.state.BookingStateInput;
import io.wyden.booking.snapshotter.domain.state.StateOutputBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;

import static io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntryType.RESERVATION_RELEASE_REMAINING;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isZero;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * Booking Engine
 * - Responsible for applying transaction logic (i.e., generated LedgerEntry objects) to positions.
 * - Tracks both AssetPositions and CashPositions.
 * - Stateless:
 * - Accepts current state (relevant Positions, LedgerEntries, open Reservation, etc.) and Command to mutate state
 * - Returns updated state to be saved
 */
@Service
public class BookingService {

    private static final Logger LOGGER = getLogger(BookingService.class);

    private final Tracing otlTracing;

    public BookingService(Tracing otlTracing) {
        this.otlTracing = otlTracing;
    }

    public StateOutputBuilder book(Collection<LedgerEntry> rawEntriesToApply, BookingStateInput stateInput) {
        try (var ignored = otlTracing.createSpan("booking-snapshotter.command.processing.engine.book", SpanKind.INTERNAL)) {
            return bookInner(rawEntriesToApply, stateInput);
        }
    }

    private record Task(LedgerEntry ledgerEntry, Position position) {
    }

    private StateOutputBuilder bookInner(Collection<LedgerEntry> rawEntriesToApply, BookingStateInput stateInput) {
        StateOutputBuilder resultBuilder = new StateOutputBuilder();

        // Merge ledger entries of the same identifier (instrument, reference, type) by summing quantity
        // This simplifies processing and makes sure multiple reservations (reservation for quantity + reservation for fee) are not skipped
        List<LedgerEntry> aggregatedEntries = LedgerEntryReducer.reduce(rawEntriesToApply).toList();

        LOGGER.trace("Getting positions for ledger entries");
        Map<LedgerEntry, Position> tasks = stateInput.getPositionsForLedgerEntries(aggregatedEntries);

        Queue<Task> sortedTasks = new LinkedList<>(tasks.entrySet().stream()
            .map(e -> new Task(e.getKey(), e.getValue()))
            .sorted(Comparator.comparing(e -> e.ledgerEntry.getType()))
            .toList());

        while (!sortedTasks.isEmpty()) {
            // process
            var task = sortedTasks.poll();
            LedgerEntry ledgerEntry = task.ledgerEntry;
            Position position = task.position();

            LOGGER.debug("Processing ledger entry: {}", ledgerEntry);
            process(ledgerEntry, position).ifPresentOrElse(processedLedgerEntry -> {
                    // apply to position
                    List<LedgerEntry> additionalEntries = applyToPosition(processedLedgerEntry, position);

                    resultBuilder.addLedgerEntry(processedLedgerEntry);
                    resultBuilder.addPosition(position);

                    // add to post-processing, if necessary
                    if (!additionalEntries.isEmpty()) {
                        StateOutputBuilder additionalEntriesResult = book(additionalEntries, stateInput);
                        resultBuilder.merge(additionalEntriesResult);
                    }
                },
                () -> LOGGER.debug("No ledger entry to apply"));
        }

        return resultBuilder;
    }

    private Optional<LedgerEntry> process(LedgerEntry ledgerEntry, Position position) {
        BigDecimal ledgerEntryQuantity = ledgerEntry.getQuantity();

        if (ledgerEntry.getType() == RESERVATION_RELEASE_REMAINING) {
            BigDecimal reservedPerReservationRef = position.getReservedQuantityMap().get(ledgerEntry.getReservationRef());
            ledgerEntryQuantity = reservedPerReservationRef == null ? null : reservedPerReservationRef.negate();
        }

        if (ledgerEntry.getType() != RESERVATION_RELEASE_REMAINING && isZero(ledgerEntryQuantity)) {
            LOGGER.debug("Empty ledger entry will be skipped from processing: {}", ledgerEntry);
            return Optional.empty();
        }

        if (position.shouldSkip(ledgerEntry)) {
            LOGGER.debug("No reservation found for ledger entry. It will not be processed: {}", ledgerEntry);
            return Optional.empty();
        }

        LedgerEntry processedLedgerEntry = new LedgerEntry(
            ledgerEntryQuantity,
            ledgerEntry.getPrice(),
            ledgerEntry.getFees(),
            ledgerEntry.getType(),
            ledgerEntry.getPortfolioId(),
            ledgerEntry.getAccountId(),
            ledgerEntry.getSymbol(),
            ledgerEntry.getReservationRef(),
            ledgerEntry.getTransactionId(),
            ledgerEntry.isSettled(),
            ledgerEntry.getSequenceNumber());

        return Optional.of(processedLedgerEntry);
    }

    /**
     * LedgerEntry -> (apply to position) -> LedgerEntry
     */
    private List<LedgerEntry> applyToPosition(LedgerEntry ledgerEntry, Position position) {
        try (var ignored = otlTracing.createSpan("booking-snapshotter.command.processing.applyledgerentry", SpanKind.CLIENT)) {
            // Perform potential post-processing:
            // - applying AssetPosition grossNewRealizedPnL to CashPosition when reducing or closing AssetPositions.
            // - store the current “qty” of adjusted Positions in the LedgerEntry balance field

            ledgerEntry.setBalanceBefore(position.getQuantity());
            LOGGER.info("Before applying ledger entry to position: {}", position);

            List<LedgerEntry> additionalLedgerEntries = position.applyLedgerEntry(ledgerEntry);
            ledgerEntry.setBalanceAfter(position.getQuantity());
            LOGGER.info(" After applying ledger entry to position: {}", position);

            if (!additionalLedgerEntries.isEmpty()) {
                LOGGER.debug("Found additional ledger entries to apply: {}", additionalLedgerEntries);
            }

            return additionalLedgerEntries;
        }
    }
}

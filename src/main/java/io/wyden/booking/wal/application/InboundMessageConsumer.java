package io.wyden.booking.wal.application;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.context.Context;
import io.wyden.booking.wal.application.deduplication.InboundMessageDeduplicator;
import io.wyden.booking.wal.domain.service.WalService;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.published.oems.OemsExecType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

import static io.wyden.booking.wal.infrastructure.telemetry.Meters.BOOKING_WAL_INCOMING_COUNT;
import static io.wyden.booking.wal.infrastructure.telemetry.Meters.bookingWalOverallLatencyTimer;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;

@Component
public class InboundMessageConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InboundMessageConsumer.class);
    public static final List<String> NOT_SUPPORTED_EXEC_TYPES = List.of(
        OemsExecType.EXEC_TYPE_UNSPECIFIED.toString(),
        OemsExecType.PENDING_NEW.toString(),
        OemsExecType.NEW.toString(),
        OemsExecType.PENDING_CANCEL.toString(),
        OemsExecType.RESTATED.toString(),
        OemsExecType.UNRECOGNIZED.toString()
    );

    private final InboundMessageDeduplicator deduplicator;
    private final WalService walService;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public InboundMessageConsumer(InboundMessageDeduplicator deduplicator,
                                  WalService walService,
                                  Telemetry telemetry) {
        this.deduplicator = deduplicator;
        this.walService = walService;
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("booking.wal.consume", SpanKind.CONSUMER, parent)) {
                return consumeInner(message, properties);
            }
        }
    }

    public ConsumptionResult consumeInner(Message message, AMQP.BasicProperties properties) {
        try {
            updateMetrics();
            recordLatencyIn(overallLatencyTimer()).of(() -> {
                if (isExcluded(properties)) {
                    LOGGER.debug("Skipping message {} with headers {} because it is not supported", message, properties.getHeaders());
                    return;
                }

                deduplicator.acceptUnique(message,
                    walService::accept,
                    m -> LOGGER.warn("Received a duplicate message, discarding: {}", m));
            });
        } catch (Exception e) {
            LOGGER.error("Could not consume message {}. Nacking without re-queue...", message, e);
            deduplicator.forget(message);
            return ConsumptionResult.failureNonRecoverable();
        }

        return ConsumptionResult.consumed();
    }

    private boolean isExcluded(AMQP.BasicProperties properties) {
        Object execType = properties.getHeaders().get(OemsHeader.OEMS_EXEC_TYPE.getHeaderName());
        if (execType != null && NOT_SUPPORTED_EXEC_TYPES.contains(execType.toString())) {
            return true;
        }

        return false;
    }

    private void updateMetrics() {
        try {
            meterRegistry.counter(BOOKING_WAL_INCOMING_COUNT).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private Timer overallLatencyTimer() {
        try {
            return bookingWalOverallLatencyTimer(meterRegistry);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}

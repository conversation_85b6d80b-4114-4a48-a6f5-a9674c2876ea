spring.application.name=booking-snapshotter
server.port=8043

# logging
logging.level.root=info
logging.level.io.wyden=debug
logging.level.io.wyden.cloudutils.telemetry=warn

rabbitmq.username=booking-snapshotter
rabbitmq.password=password
rabbitmq.virtualHost=/
rabbitmq.host=localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port=5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls=
rabbitmq.producer-count=5

spring.datasource.url=*************************************************
spring.datasource.username=booking_snapshot
spring.datasource.password=password

spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

spring.flyway.enabled=true
spring.flyway.locations=classpath:psql/migration/schema,classpath:psql/migration/data
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=1

management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.endpoint.health.group.liveness.include=livenessState,rabbit,clusterRunning,diskSpace,hazelcast
management.endpoint.health.group.readiness.include=readinessState,referenceDataRest
management.metrics.tags.wyden_service=booking-snapshotter

hz.addressList=localhost
hz.outboundPortDefinition=

access.gateway.host=http://localhost:8089
booking.wal.host=http://localhost:8042
license.manager.host=https://license.wyden.io
reference.data.url=http://localhost:8098
tracing.collector.endpoint=http://localhost:4317

queue.wal-event=booking.booking-snapshotter-queue.booking-wal.WAL-EVENT
queue.volume-reporting-request=booking-snapshotter-queue.message-scheduler.%s.VOLUME-REPORTING-REQUEST

wal.recovery.batch-size=1000
wal.recovery.onstartup=false
wal.onerror.requeue = false
wal.onerror.break = false

# period between invocations, in milliseconds
#cqrs.snapshotting.rateInMs=50
#cqrs.snapshotting.batchSize=200
# Supported values: { reactive, rate, none }
cqrs.persisting.strategy=reactive
# Used in rate persisting strategy
cqrs.persisting.strategy.rateInMs=200
# Set to 0 to drain the persistence queue fully in every run
cqrs.persisting.strategy.rateMaxBatchSize=1000
# If enabled, persisting will stop on saving errors and won't apply any further state changes to the DB
cqrs.persisting.breakOnError=false
# Capacity of the in-memory queue of StateOutputs awaiting persistence.
# If the queue is full, then the Snapshotting thread will block and wait until the queued StateOutputs are consumed and persisted into the DB.
cqrs.persisting.queue.capacity=100000

# Gets Duration from String such as PnDTnHnMn.nS
# See java.time.Duration#parse for more details
# Set PT0m to disable eviction
# Note: Reservation entries will be additionally early evicted after Order's terminal state is detected (FILLED, CANCELLED, REJECTED, etc)
cache.reservation.eviction.ttl.duration=PT1M

# Gets Duration from String such as PnDTnHnMn.nS
# See java.time.Duration#parse for more details
# Set PT0m to disable eviction
cache.position.eviction.ttl.duration=PT0m

# volume reporting is supported by booking-engine - enable here, once booking-engine is decommissioned
volume-reporting.enabled=false
# volume reporting every day at 02:00
volume-reporting.cron-expression=0 0 2 * * *
volume-reporting.message-id=booking-snapshotter.volume-reporting
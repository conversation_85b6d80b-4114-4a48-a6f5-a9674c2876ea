spring.application.name=booking-wal

server.port=8042

management.endpoint.health.enabled=true
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=${spring.application.name}
management.endpoint.health.group.liveness.include=livenessState,rabbit,diskSpace
management.endpoint.health.group.readiness.include=readinessState

rabbitmq.username = booking-wal
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

# Inbound message queue
rabbitmq.booking.wal.queue = booking.booking-wal-inbound-queue.v1.ALL

db.engine=psql
database.host=localhost
database.name=booking_wal
spring.datasource.url=jdbc:postgresql://${database.host}:5432/${database.name}
spring.datasource.username=booking_wal
spring.datasource.password=password

spring.flyway.url=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}
spring.flyway.enabled=true
spring.flyway.locations=classpath:psql/migration/schema,classpath:psql/migration/data

tracing.collector.endpoint=http://localhost:4317

hz.addressList=localhost
hz.outboundPortDefinition=
hz.map.messagededuplication.ttl=PT24H

# Default pagination values for WAL events
wal.events.default.first=1000
wal.events.default.after=0

# Migration from legacy booking-engine
migration.bookingengine.completed = false
booking.engine.host=http://localhost:8100
migration.bookingengine.url=${booking.engine.host}/wal-events?first=%d&after=%d
migration.bookingengine.batchsize=1000
package io.wyden.test.scenariorunner.settlement;

import io.qameta.allure.Epic;
import io.wyden.settlement.client.run.SettlementRunResponse;
import io.wyden.settlement.client.run.leg.SettlementLegResponse;
import io.wyden.settlement.client.settlementconfiguration.DayOfTheWeek;
import io.wyden.settlement.client.settlementconfiguration.SettlementAccountConfigurationInput;
import io.wyden.settlement.client.settlementconfiguration.SettlementConfigurationInput;
import io.wyden.settlement.client.settlementconfiguration.SettlementConfigurationResponse;
import io.wyden.settlement.client.settlementconfiguration.SettlementSchedulePoint;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountFactory;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.eachtest.VenueAccountExtension;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.junit.jupiter.api.parallel.Isolated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static io.wyden.published.settlement.SettlementStatus.COMPLETED;
import static io.wyden.published.settlement.SettlementStatus.PENDING;
import static io.wyden.test.scenariorunner.data.infra.Epics.SETTLEMENTS;
import static io.wyden.test.scenariorunner.data.infra.TestTags.BETA;
import static org.assertj.core.api.Assertions.assertThat;

@Isolated
@GraphQL
@Epic(SETTLEMENTS)
public class AutomatedSettlementsTest extends SettlementsTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutomatedSettlementsTest.class);
    private static final String TIMEZONE = "Europe/Warsaw";

    @Test
    public void createSettlementScheduleForUnsettledTransactionsWhenTimeIsReachedThenSettlementRunWithLegsIsInStatusPending(ClientSession client, ConnectorMockSession conn) {
        sendOrderAndWaitTransactionInSettlement(client, conn);

        SettlementAccountConfigurationInput settlementAccountConfigurationInput = getSettlementAccountConfigurationWithCurrentDateTimePlusThreeSeconds();
        SettlementConfigurationInput settlementConfigurationInput = new SettlementConfigurationInput(venueAccountName, settlementAccountConfigurationInput);

        graphQLActor.settlements().updateSettlementSchedule(List.of(settlementConfigurationInput));

        assertThat(graphQLActor.settlements().getSettlementConfiguration())
            .extracting(SettlementConfigurationResponse::accountId)
            .contains(venueAccountName);

        assertThat(graphQLActor.settlements().getSettlementConfiguration())
            .filteredOn(settlementConfigurationResponse -> settlementConfigurationResponse.accountId().equals(venueAccountName))
            .extracting(SettlementConfigurationResponse::config)
            .satisfiesExactly(config -> {
                assertThat(config.scheduleTZid()).isEqualTo(TIMEZONE);
                assertThat(config.schedule()).isEqualTo(settlementAccountConfigurationInput.schedule());
            });

        SettlementRunResponse settlementRunResponse = getSettlementRunResponse();

        assertThat(settlementRunResponse.status())
            .isEqualTo(PENDING);

        assertThat(settlementRunResponse.legs())
            .extracting(SettlementLegResponse::status)
            .containsOnly(PENDING);
    }

    @Disabled("IT IS NOT POSSIBLE TO CHECK A SETTLEMENT CREATED FROM SCHEDULE IF IT HAS NO LEGS")
    @Test
    public void createSettlementScheduleForSettledTransactionsWhenTimeIsReachedThenSettlementRunIsCreatedWithStatusCompleted() {
        SettlementAccountConfigurationInput settlementAccountConfigurationInput = getSettlementAccountConfigurationWithCurrentDateTimePlusThreeSeconds();
        SettlementConfigurationInput settlementConfigurationInput = new SettlementConfigurationInput(venueAccountName, settlementAccountConfigurationInput);

        graphQLActor.settlements().updateSettlementSchedule(List.of(settlementConfigurationInput));

        assertThat(graphQLActor.settlements().getSettlementConfiguration())
            .extracting(SettlementConfigurationResponse::accountId)
            .contains(venueAccountName);

        assertThat(graphQLActor.settlements().getSettlementConfiguration())
            .filteredOn(settlementConfigurationResponse -> settlementConfigurationResponse.accountId().equals(venueAccountName))
            .extracting(SettlementConfigurationResponse::config)
            .satisfiesExactly(config -> {
                assertThat(config.scheduleTZid()).isEqualTo(TIMEZONE);
                assertThat(config.schedule()).isEqualTo(settlementAccountConfigurationInput.schedule());
            });

        assertThat(getSettlementRunFromHistory().status())
            .isEqualTo(COMPLETED);
    }

    @Test
    public void createSettlementScheduleWhenSendingEmptyConfigurationThenSettlementScheduleIsRemoved() {
        SettlementAccountConfigurationInput settlementAccountConfigurationInput = getSettlementAccountConfigurationWithCurrentDateTimePlusThreeSeconds();
        SettlementConfigurationInput settlementConfigurationInput = new SettlementConfigurationInput(venueAccountName, settlementAccountConfigurationInput);

        graphQLActor.settlements().updateSettlementSchedule(List.of(settlementConfigurationInput));

        assertThat(graphQLActor.settlements().getSettlementConfiguration())
            .extracting(SettlementConfigurationResponse::accountId)
            .contains(venueAccountName);

        SettlementAccountConfigurationInput deletingSettlementAccountConfigurationInput = getEmptySettlementAccountConfiguration();
        SettlementConfigurationInput deletingSettlementConfigurationInput = new SettlementConfigurationInput(venueAccountName, deletingSettlementAccountConfigurationInput);

        graphQLActor.settlements().updateSettlementSchedule(List.of(deletingSettlementConfigurationInput));

        assertThat(graphQLActor.settlements().getSettlementConfiguration())
            .extracting(SettlementConfigurationResponse::accountId)
            .doesNotContain(venueAccountName);
    }

    private @NotNull SettlementAccountConfigurationInput getSettlementAccountConfigurationWithCurrentDateTimePlusThreeSeconds() {
        return new SettlementAccountConfigurationInput(
            false,
            TIMEZONE,
            List.of(new SettlementSchedulePoint(getCurrentDayOfTheWeek(), getCurrentTimePlusThreeSeconds())),
            null,
            null,
            null,
            null,
            false,
            null
        );
    }

    private @NotNull SettlementAccountConfigurationInput getEmptySettlementAccountConfiguration() {
        return new SettlementAccountConfigurationInput(
            false,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            null
        );
    }

    private @NotNull String getCurrentTimePlusThreeSeconds() {
        String currentTimePlusThreeSeconds = LocalDateTime.now(ZoneId.of(TIMEZONE)).plusSeconds(3).format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        LOGGER.info("Time of scheduled settlement: {}",currentTimePlusThreeSeconds);
        return currentTimePlusThreeSeconds;
    }

    private @NotNull DayOfTheWeek getCurrentDayOfTheWeek() {
        return DayOfTheWeek.valueOf(LocalDateTime.now().getDayOfWeek().name());
    }

    @GraphQL
    @Nested
    @Epic(SETTLEMENTS)
    @Tag(BETA)
    class MultipleAccountAutomatedSettlements {

        private final String SECOND_VENUE_ACCOUNT = VenueAccountFactory.randomVenueAccountName(VenueAccountConstants.MOCK_VENUE);
        @Order(6)
        @RegisterExtension
        VenueAccountExtension secondAccountExtension = new VenueAccountExtension(
            clientActorExtension.clientId(),
            Map.of(VenueAccountConstants.MOCK_VENUE, List.of(SECOND_VENUE_ACCOUNT))
        );

        @Test
        public void createSettlementScheduleForTwoAccountsThenTwoSettlementConfigurationsAreCreated() {
            SettlementAccountConfigurationInput settlementAccountConfigurationInput = getSettlementAccountConfigurationWithCurrentDateTimePlusThreeSeconds();
            SettlementConfigurationInput settlementConfigurationInput = new SettlementConfigurationInput(venueAccountName, settlementAccountConfigurationInput);
            SettlementConfigurationInput settlementConfigurationInput2 = new SettlementConfigurationInput(SECOND_VENUE_ACCOUNT, settlementAccountConfigurationInput);

            graphQLActor.settlements().updateSettlementSchedule(List.of(settlementConfigurationInput, settlementConfigurationInput2));

            assertThat(graphQLActor.settlements().getSettlementConfiguration())
                .extracting(SettlementConfigurationResponse::accountId)
                .contains(venueAccountName, SECOND_VENUE_ACCOUNT);

            assertThat(graphQLActor.settlements().getSettlementConfiguration())
                .filteredOn(settlementConfigurationResponse -> settlementConfigurationResponse.accountId().equals(venueAccountName))
                .extracting(SettlementConfigurationResponse::config)
                .satisfiesExactly(config -> {
                    assertThat(config.scheduleTZid()).isEqualTo(TIMEZONE);
                    assertThat(config.schedule()).isEqualTo(settlementAccountConfigurationInput.schedule());
                });

            assertThat(graphQLActor.settlements().getSettlementConfiguration())
                .filteredOn(settlementConfigurationResponse -> settlementConfigurationResponse.accountId().equals(SECOND_VENUE_ACCOUNT))
                .extracting(SettlementConfigurationResponse::config)
                .satisfiesExactly(config -> {
                    assertThat(config.scheduleTZid()).isEqualTo(TIMEZONE);
                    assertThat(config.schedule()).isEqualTo(settlementAccountConfigurationInput.schedule());
                });
        }
    }
}

package io.wyden.test.scenariorunner.settlement;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.settlement.client.run.SettlementRunResponse;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.annotation.TestSetup;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.gqlclient.searchinput.SettlementTransactionSearchInputs;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.trading.TradingTestBase;
import io.wyden.test.scenariorunner.util.MessageQueue;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;

import java.time.Duration;
import java.util.List;
import java.util.function.Predicate;

import static io.wyden.test.scenariorunner.data.infra.TestTags.BETA;
import static io.wyden.test.scenariorunner.data.refdata.Currency.BTC;
import static io.wyden.test.scenariorunner.data.refdata.Currency.USD;

@TestSetup
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.ORDER_COLLIDER,
    Service.ORDER_GATEWAY,
    Service.REFERENCE_DATA,
    Service.BOOKING_ENGINE,
    Service.CONNECTOR_WRAPPER_MOCK,
    Service.REST_API_SERVER,
    Service.SETTLEMENT,
    Service.BOOKING_SNAPSHOTTER,
    Service.BOOKING_WAL,
    Service.MESSAGE_SCHEDULER,
    Service.STORAGE
})
@Tag(BETA)
public abstract class SettlementsTestBase extends TradingTestBase {
    protected static final String CANCEL_REJECT_MESSAGE = "Run is in state: RunStateInProgress cannot perform cancel";
    protected static final double TRADE_QUANTITY = 1;
    protected static final double TRADE_PRICE = 10000;
    protected static final String RECEIVE_CURRENCY = BTC.name();
    protected static final String SEND_CURRENCY = USD.name();

    protected String venueAccountName;
    protected GraphQLActor graphQLActor;
    protected MessageQueue<SettlementRunResponse> settlementRunQueue = new MessageQueue<>("Settlement");

    protected static String getCurrentTime() {
        return DateUtils.toEpochMillis();
    }

    @BeforeEach
    public void setUp(GraphQLActor actor) {
        graphQLActor = actor;
        venueAccountName = connectorSessionExtension.venueAccountName();
        settlementRunQueue.connect(graphQLActor.settlements().getSettlementRuns());
    }

    @AfterEach
    void disconnect() {
        settlementRunQueue.disconnect();
    }

    protected void sendOrderAndWaitTransactionInSettlement(ClientSession client, ConnectorMockSession conn) {
        client.sendDefaultStreetLimitOrder(TRADE_QUANTITY);
        conn.acceptNewOrder();
        conn.fillFull(TRADE_QUANTITY, TRADE_PRICE);
        WaitUtils.justWait(Duration.ofSeconds(2));
        waitForSettlementTransactions();
    }

    protected SettlementRunResponse getSettlementRunResponse(Predicate<SettlementRunResponse> predicate) {
        return WaitUtils.awaitMsgFromQueue(settlementRunQueue, predicate);
    }

    protected SettlementRunResponse getSettlementRunResponse(String settlementRunId) {
        return WaitUtils.awaitMsgFromQueue(settlementRunQueue, settlementRunResponse -> settlementRunResponse.id().equals(settlementRunId));
    }

    protected SettlementRunResponse getSettlementRunResponse() {
        return WaitUtils.awaitMsgFromQueue(settlementRunQueue, hasMatchingVenueAccount(venueAccountName));
    }

    protected SettlementRunResponse getSettlementRunFromHistory(String settlementRunId) {
        String currentTime = getCurrentTime();

        List<SettlementRunResponse> settlementHistoryRunResponseList = graphQLActor.settlements().getSettlementHistoryRun(currentTime);

        return settlementHistoryRunResponseList
            .stream()
            .filter(settlementRunResponse -> settlementRunResponse.id().equals(settlementRunId))
            .findFirst()
            .orElseThrow();
    }

    protected SettlementRunResponse getSettlementRunFromHistory() {
        String currentTime = getCurrentTime();

        List<SettlementRunResponse> settlementHistoryRunResponseList = graphQLActor.settlements().getSettlementHistoryRun(currentTime);

        return settlementHistoryRunResponseList
            .stream()
            .filter(hasMatchingVenueAccount(venueAccountName))
            .findFirst()
            .orElseThrow();
    }

    private Predicate<SettlementRunResponse> hasMatchingVenueAccount(String venueAccountName) {
        return settlementRunResponse ->
            settlementRunResponse.legs()
                .stream()
                .anyMatch(settlementLeg ->
                    settlementLeg.venueAccountName().equals(venueAccountName)
                );
    }

    private void waitForSettlementTransactions() {
        WaitUtils.waitUntilIgnoringExceptions("settlement transactions for [%s] to be available".formatted(venueAccountName),
            () -> !graphQLActor
                .settlements()
                .getSettlementTransactions(
                    SettlementTransactionSearchInputs
                        .byAccounts(List.of(venueAccountName))
                ).getAllNodes().isEmpty()
        );
    }

}

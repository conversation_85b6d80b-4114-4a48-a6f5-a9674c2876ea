package io.wyden.test.scenariorunner;

import io.qameta.allure.Epic;
import io.wyden.test.scenariorunner.integration.service.ServiceHealthObserver;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import static io.wyden.test.scenariorunner.data.infra.Epics.INFRA;

@Epic(INFRA)
@Disabled("Disabled to not rework health observer for beta services")
public class HealthTest {

    private final ServiceHealthObserver serviceHealthObserver = new ServiceHealthObserver();

    @Test
    void healthTest() {
        serviceHealthObserver.atServicesShouldBeUp();
    }

}

package io.wyden.test.scenariorunner.referencedata;

import io.qameta.allure.Epic;
import io.wyden.apiserver.rest.apiui.SharedModel.SortingOrder;
import io.wyden.apiserver.rest.referencedata.portfolio.model.CreatePortfolioDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSearchInputDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSortBy;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioTypeDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.TagDto;
import io.wyden.apiserver.rest.security.model.Resource;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.test.scenariorunner.data.refdata.PortfolioFactory;
import io.wyden.test.scenariorunner.extension.alltest.GraphQLActorExtension;
import io.wyden.test.scenariorunner.extension.alltest.SecurityIntegratorExtension;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.infra.OemsHealthObserverExtension;
import io.wyden.test.scenariorunner.integration.ClientActorFactory;
import io.wyden.test.scenariorunner.integration.SecurityIntegrator;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.util.Randomizer;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

import static io.wyden.test.scenariorunner.data.infra.Epics.REFERENCE_DATA;
import static io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PortfolioSearchInputs.byName;
import static io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PortfolioSearchInputs.byPortfolioIds;
import static io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PortfolioSearchInputs.byPortfolioType;
import static io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PortfolioSearchInputs.byScopes;
import static io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PortfolioSearchInputs.first;
import static io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PortfolioSearchInputs.search;
import static java.util.Collections.reverseOrder;
import static org.assertj.core.api.Assertions.assertThat;

@Epic(REFERENCE_DATA)
@ExtendWith({
    OemsHealthObserverExtension.class,
    SecurityIntegratorExtension.class
})
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.REST_API_SERVER,
    Service.REFERENCE_DATA,
    Service.STORAGE
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@GraphQL
public class PortfolioSearchTest {

    @Order(1)
    @RegisterExtension
    static GraphQLActorExtension ownerOfPortfolioAndAccountExtension = new GraphQLActorExtension();

    protected static GraphQLActor actorPortfoliosOwner;
    protected static GraphQLActor actorWithReadPermissionsToPortfolio_3;
    protected static CreatePortfolioDto PORTFOLIO_1;
    protected static CreatePortfolioDto PORTFOLIO_2;
    protected static CreatePortfolioDto PORTFOLIO_3;
    private static final int DEFAULT_RESULTS_LIMIT = 100;
    protected static List<String> CREATED_PORTFOLIOS;

    @BeforeAll
    static void setupActor(SecurityIntegrator securityIntegrator) {
        actorPortfoliosOwner = ownerOfPortfolioAndAccountExtension.actor();
        PORTFOLIO_1 = PortfolioFactory.randomCreatePortfolio();
        actorPortfoliosOwner.portfolio().create(PORTFOLIO_1);
        PORTFOLIO_2 = PortfolioFactory.randomCreatePortfolio();
        actorPortfoliosOwner.portfolio().create(PORTFOLIO_2);
        PORTFOLIO_3 = PortfolioFactory.randomCreatePortfolio(PortfolioFactory.uniquePortfolioName(), PortfolioTypeDto.NOSTRO);
        actorPortfoliosOwner.portfolio().create(PORTFOLIO_3);
        actorWithReadPermissionsToPortfolio_3 = ClientActorFactory.createActorWithDynamicSharedReadPermissions(
            ownerOfPortfolioAndAccountExtension.actor(), securityIntegrator, Resource.PORTFOLIO, PORTFOLIO_3.name());
        CREATED_PORTFOLIOS = List.of(PORTFOLIO_1.name(), PORTFOLIO_2.name(), PORTFOLIO_3.name());
    }

    @Test
    void searchWithNoFilters() {
        // given
        PortfolioSearchInputDto searchInput = first(Integer.MAX_VALUE);

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios.stream().map(PortfolioResponseDto::id))
            .contains(PORTFOLIO_1.name(), PORTFOLIO_2.name(), PORTFOLIO_3.name());
    }

    @Test
    void filterByName() {
        // given
        PortfolioSearchInputDto searchInput = byName(PORTFOLIO_1.name());

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.name().contains(PORTFOLIO_1.name()));

        // given
        searchInput = byName(PortfolioFactory.uniquePortfolioName());

        // when
        filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isEmpty();

        // given
        String portfolio_2_substring = PORTFOLIO_2.name().substring(0, PORTFOLIO_2.name().length() - 2);
        searchInput = byName(portfolio_2_substring);

        // when
        filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios.stream().map(PortfolioResponseDto::id))
            .contains(PORTFOLIO_2.name());
        assertThat(filteredPortfolios).allMatch(p -> p.name().contains(portfolio_2_substring));
    }

    @Test
    void filterById() {
        // given
        PortfolioSearchInputDto searchInput = byPortfolioIds(List.of(PORTFOLIO_2.name()));

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.id().contains(PORTFOLIO_2.name()));

        // given
        searchInput = byPortfolioIds(List.of(Randomizer.randomString(), PORTFOLIO_1.name()));

        // when
        filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.id().contains(PORTFOLIO_1.name()));
    }

    @Test
    void filterByScope() {
        // given
        PortfolioSearchInputDto searchInput = byScopes(List.of(Scope.MANAGE));

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.scopes().contains(Scope.MANAGE));

        // given
        searchInput = byScopes(List.of(Scope.READ));

        // when
        filteredPortfolios = actorWithReadPermissionsToPortfolio_3.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.scopes().contains(Scope.READ));
    }

    @Test
    void filterByTag() {
        // given
        TagDto firstPortfolioTag = PORTFOLIO_1.tags().get(0);
        PortfolioSearchInputDto searchInput = new PortfolioSearchInputDto(
            null, null, null, List.of(firstPortfolioTag.value()), null, null, null, DEFAULT_RESULTS_LIMIT, "");

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.tags().contains(firstPortfolioTag));

        // given
        TagDto secondPortfolioTag = PORTFOLIO_2.tags().get(0);
        searchInput = new PortfolioSearchInputDto(
            null, null, null, List.of(firstPortfolioTag.value(), secondPortfolioTag.value()), null, null, null, DEFAULT_RESULTS_LIMIT, "");

        // when
        filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.tags().contains(firstPortfolioTag) || p.tags().contains(secondPortfolioTag));
    }

    @Test
    void filterByPortfolioType() {
        // given
        PortfolioSearchInputDto searchInput = byPortfolioType(PortfolioTypeDto.NOSTRO);

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.portfolioType().equals(PortfolioTypeDto.NOSTRO));

        // given
        searchInput = byPortfolioType(PortfolioTypeDto.VOSTRO);

        // when
        filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.portfolioType().equals(PortfolioTypeDto.VOSTRO));
    }

    @Test
    void limitResults() {
        // given
        int resultsLimit = 2;
        PortfolioSearchInputDto searchInput = first(resultsLimit);

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).hasSize(resultsLimit);
    }

    @Test
    void filterByAfterAscSortingByCreatedAtWithLastPage() {
        // given
        int resultsLimit = 2;
        PortfolioSearchInputDto searchInput = search(null, CREATED_PORTFOLIOS, null, null, null, PortfolioSortBy.CREATED_AT, SortingOrder.ASC, resultsLimit, null);

        // when
        var firstPageCursor = actorPortfoliosOwner.portfolio().search(searchInput);

        // then
        assertThat(firstPageCursor.pageInfo().hasNextPage()).isTrue();

        // given
        searchInput = search(null, CREATED_PORTFOLIOS, null, null, null, PortfolioSortBy.CREATED_AT, SortingOrder.ASC, resultsLimit, firstPageCursor.pageInfo().endCursor());

        // when
        var secondPageCursor = actorPortfoliosOwner.portfolio().search(searchInput);

        // then
        assertThat(secondPageCursor.pageInfo().hasNextPage()).isFalse();
        assertThat(secondPageCursor.getAllNodes().size()).isEqualTo(1);
        assertThat(secondPageCursor.getAllNodes())
            .extracting(PortfolioResponseDto::name)
            .containsOnly(PORTFOLIO_3.name());
    }

    @Test
    void filterByAfterDescSortingByCreatedAtWithMiddlePage() {
        // given
        int resultsLimit = 1;
        PortfolioSearchInputDto searchInput = search(null, CREATED_PORTFOLIOS, null, null, null, PortfolioSortBy.CREATED_AT, SortingOrder.DESC, resultsLimit, null);

        // when
        var firstPageCursor = actorPortfoliosOwner.portfolio().search(searchInput);

        // then
        assertThat(firstPageCursor.pageInfo().hasNextPage()).isTrue();
        assertThat(firstPageCursor.getAllNodes())
            .extracting(PortfolioResponseDto::name)
            .containsOnly(PORTFOLIO_3.name());

        // given
        searchInput = search(null, CREATED_PORTFOLIOS, null, null, null, PortfolioSortBy.CREATED_AT, SortingOrder.DESC, resultsLimit, firstPageCursor.pageInfo().endCursor());

        // when
        var secondPageCursor = actorPortfoliosOwner.portfolio().search(searchInput);

        // then
        assertThat(secondPageCursor.pageInfo().hasNextPage()).isTrue();
        assertThat(secondPageCursor.getAllNodes().size()).isEqualTo(1);
        assertThat(secondPageCursor.getAllNodes())
            .extracting(PortfolioResponseDto::name)
            .containsOnly(PORTFOLIO_2.name());
    }

    @Test
    void filterByAllCriteria() {
        // given
        TagDto firstPortfolioTag = PORTFOLIO_1.tags().get(0);
        PortfolioSearchInputDto searchInput = search(null,
            List.of(PORTFOLIO_1.name(), PORTFOLIO_2.name()),
            List.of(Scope.MANAGE, Scope.TRADE),
            List.of(firstPortfolioTag.value()),
            PORTFOLIO_1.portfolioType(),
            null,
            null,
            DEFAULT_RESULTS_LIMIT,
            null);

        // when
        Collection<PortfolioResponseDto> filteredPortfolios = actorPortfoliosOwner.portfolio().search(searchInput).getAllNodes();

        // then
        assertThat(filteredPortfolios).isNotEmpty();
        assertThat(filteredPortfolios).allMatch(p -> p.name().contains(PORTFOLIO_1.name()));
        assertThat(filteredPortfolios).allMatch(p -> p.id().contains(PORTFOLIO_1.name()) || p.id().contains(PORTFOLIO_2.name()));
        assertThat(filteredPortfolios).allMatch(p -> p.scopes().contains(Scope.MANAGE) || p.scopes().contains(Scope.TRADE));
        assertThat(filteredPortfolios).allMatch(p -> p.tags().contains(firstPortfolioTag));
        assertThat(filteredPortfolios).allMatch(p -> p.portfolioType().equals(PORTFOLIO_1.portfolioType()));
        assertThat(filteredPortfolios).hasSizeLessThanOrEqualTo(DEFAULT_RESULTS_LIMIT);
    }

    @ParameterizedTest(name = "sortingBy {0}")
    @MethodSource
    void sortingBy(PortfolioSortBy sortBy, String comparedField) {
        // desc
        PortfolioSearchInputDto searchInput = search(sortBy, SortingOrder.DESC, DEFAULT_RESULTS_LIMIT, null);

        var searchResult = actorPortfoliosOwner.portfolio().search(searchInput);

        assertThat(searchResult.getAllNodes())
            .extracting(comparedField)
            .isSortedAccordingTo(reverseOrder());

        // asc
        searchInput = search(sortBy, SortingOrder.ASC, DEFAULT_RESULTS_LIMIT, null);

        searchResult = actorPortfoliosOwner.portfolio().search(searchInput);

        assertThat(searchResult.getAllNodes())
            .extracting(comparedField)
            .isSorted();
    }

    private static Stream<Arguments> sortingBy() {
        return Stream.of(
            Arguments.of(PortfolioSortBy.CREATED_AT, "createdAt"),
            Arguments.of(PortfolioSortBy.PORTFOLIO_ID, "id"),
            Arguments.of(PortfolioSortBy.PORTFOLIO_NAME, "name")
        );
    }

}

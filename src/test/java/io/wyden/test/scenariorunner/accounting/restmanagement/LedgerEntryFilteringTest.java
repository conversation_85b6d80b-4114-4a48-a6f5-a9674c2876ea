package io.wyden.test.scenariorunner.accounting.restmanagement;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.cloud.utils.rest.pagination.PaginationModel.CursorConnection;
import io.wyden.rest.management.domain.LedgerEntryModel;
import io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntry;
import io.wyden.rest.management.domain.TransactionModel;
import io.wyden.test.scenariorunner.extension.alltest.RestManagementActorExtension;
import io.wyden.test.scenariorunner.integration.restmgmtclient.RestManagementActor;
import io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.TransactionSearchInputs;
import io.wyden.test.scenariorunner.trading.HistoryBase;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Stream;

import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.ASSET_TRADE_BUY;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.ASSET_TRADE_PROCEEDS;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.ASSET_TRADE_SELL;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.DEPOSIT;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.DEPOSIT_FEE;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.FEE;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.TRADING_FEE;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.TRANSFER;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.TRANSFER_FEE;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.WITHDRAWAL;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.WITHDRAWAL_FEE;
import static io.wyden.rest.management.domain.LedgerEntryModel.LedgerEntryType.WITHDRAWAL_RESERVATION;
import static io.wyden.rest.management.domain.TransactionModel.TransactionType.STREET_CASH_TRADE;
import static io.wyden.test.scenariorunner.data.infra.Epics.REST_MANAGEMENT;
import static io.wyden.test.scenariorunner.data.infra.TestTags.BPCE;
import static io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.LedgerEntrySearchInputs.byCurrency;
import static io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.LedgerEntrySearchInputs.byReservationRef;
import static io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.LedgerEntrySearchInputs.byTransactionId;
import static io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.LedgerEntrySearchInputs.byType;
import static io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.LedgerEntrySearchInputs.fromTo;
import static io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.LedgerEntrySearchInputs.page;
import static org.assertj.core.api.Assertions.assertThat;

@Epic(REST_MANAGEMENT)
@Tag(BPCE)
public class LedgerEntryFilteringTest extends HistoryBase {

    @RegisterExtension
    RestManagementActorExtension restManagementActorExtension = new RestManagementActorExtension(ownerOfPortfolioAndAccountExtension.clientId());

    private RestManagementActor restMgmtActor;

    @BeforeAll
    void setupRestMgmtActor() {
        this.restMgmtActor = restManagementActorExtension.actor();
    }

    @Test
    void filteringByExistingCurrency_shouldReturnOnlyRequestedCurrencyLedgerEntries() {

        String requestedCurrency = tradedInstrument2.getTicker().getBase();

        Collection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(
            byCurrency(requestedCurrency)
        ).getAllNodes();

        assertThat(ledgerEntries)
            .isNotEmpty()
            .extracting(LedgerEntry::currency)
            .containsOnly(requestedCurrency);
    }

    @Test
    void filteringByExistingLedgerEntryType_shouldReturnOnlyRequestedTypeLedgerEntries() {

        LedgerEntryModel.LedgerEntryType requestedType = LedgerEntryModel.LedgerEntryType.CASH_TRADE_CREDIT;

        Collection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(
            byType(requestedType)
        ).getAllNodes();

        assertThat(ledgerEntries)
            .isNotEmpty()
            .extracting(LedgerEntry::type)
            .containsOnly(requestedType);
    }

    @Test
    void filteringToNow_shouldReturnLedgerEntriesCreatedEarlierThenNow() {
        long now = System.currentTimeMillis();

        Collection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(
            fromTo(null, now)
        ).getAllNodes();

        assertThat(ledgerEntries)
            .isNotEmpty()
            .extracting(LedgerEntry::dateTime)
            .allSatisfy(date -> assertThat(date).isLessThanOrEqualTo(now));
    }

    @ParameterizedTest(name = "filtering{0}_shouldReturnEmptyList")
    @MethodSource
    void filteringFromTo_shouldBeEmpty(String description, LedgerEntryModel.LedgerEntrySearch search) {

        Collection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(search).getAllNodes();

        assertThat(ledgerEntries)
            .isEmpty();
    }

    private static Stream<Arguments> filteringFromTo_shouldBeEmpty() {
        return Stream.of(
            Arguments.of("FromTomorrow", fromTo(Instant.now().plus(1, ChronoUnit.DAYS).toEpochMilli(), null)),
            Arguments.of("ToYesterday", fromTo(null, Instant.now().minus(1, ChronoUnit.DAYS).toEpochMilli()))
        );
    }

    @Test
    void filteringFromAndToDate_shouldReturnLedgerEntriesCreatedLaterThenOrEqualToFromDateAndEarlierThenToDate() {

        long firstOrderEpoch = mockOrderPrefillExtension.getOrderTimes().get(0).getTime();
        long secondOrderEpoch = mockOrderPrefillExtension.getOrderTimes().get(1).getTime();

        List<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(
                fromTo(firstOrderEpoch, secondOrderEpoch)
            ).getAllNodes().stream()
            .sorted(Comparator.comparingLong(LedgerEntry::dateTime))
            .toList();

        long from = ledgerEntries.get(0).dateTime();
        long to = ledgerEntries.get(1).dateTime();

        Collection<LedgerEntry> ledgerEntriesFromTo = restMgmtActor.ledgerEntries().search(
            fromTo(from, to)
        ).getAllNodes();

        assertThat(ledgerEntriesFromTo)
            .hasSizeGreaterThanOrEqualTo(1)
            .extracting(LedgerEntry::dateTime)
            .allSatisfy(dateTime -> assertThat(dateTime)
                .isGreaterThanOrEqualTo(from)
                .isLessThan(to));
    }

    @Test
    void filteringByExistingTransactionId_shouldReturnOnlyLedgerEntriesWithRequestedTransactionId() {

        TransactionModel.StreetCashTrade transaction = findOneStreetCashTradeTransaction();

        String requestedTransactionId = transaction.id();

        Collection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(
            byTransactionId(requestedTransactionId)
        ).getAllNodes();

        assertThat(ledgerEntries)
            .isNotEmpty()
            .extracting(LedgerEntry::transactionId)
            .containsOnly(requestedTransactionId);
    }

    @Test
    void filteringByReservationRef_shouldReturnOnlyLedgerEntriesWithRequestedReservationRef() {

        TransactionModel.StreetCashTrade streetCashTrade = findOneStreetCashTradeTransaction();

        // reservationRef is orderId
        String requestedOrderId = streetCashTrade.reservationRef();

        Collection<LedgerEntry> reservationRefLedgerEntries = restMgmtActor.ledgerEntries().search(
            byReservationRef(requestedOrderId)
        ).getAllNodes();

        assertThat(reservationRefLedgerEntries)
            .isNotEmpty()
            .extracting(LedgerEntry::reservationRef)
            .containsOnly(requestedOrderId);
    }

    @Step
    private TransactionModel.StreetCashTrade findOneStreetCashTradeTransaction() {
        return restMgmtActor.transactions().getStreetCashTransactions(
                TransactionSearchInputs.byTransactionType(1, STREET_CASH_TRADE))
            .getAllNodes()
            .stream()
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("STREET_CASH_TRADE transactions for user=%s not found".formatted(restMgmtActor.getClientId())));
    }

    @ParameterizedTest(name = "shouldReturnLedgerEntryType {0}")
    @EnumSource(value = LedgerEntryModel.LedgerEntryType.class,
                mode = EnumSource.Mode.EXCLUDE, names = {"UNSPECIFIED"})
    void allLEdgerEntryTypesShouldReturn(LedgerEntryModel.LedgerEntryType type) {

        List<LedgerEntry> result = restMgmtActor.ledgerEntries().search(
            byType(1, type)).getAllNodes().stream().toList();

        List<LedgerEntryModel.LedgerEntryType> absentLedgerEntries = List.of(
            // asset trading is not supported by the platform yet
            ASSET_TRADE_BUY, ASSET_TRADE_SELL, ASSET_TRADE_PROCEEDS,
            // no LEs generated during tests yet
            FEE, WITHDRAWAL_RESERVATION, WITHDRAWAL, DEPOSIT,
            TRANSFER, DEPOSIT_FEE, TRANSFER_FEE, WITHDRAWAL_FEE, TRADING_FEE
        );

        if (absentLedgerEntries.contains(type)) {
            // only check query compatibility, there will be no returned result in the DB to assert though...
            assertThat(result).isNotNull();
        } else {
            assertThat(result).hasSize(1);
            assertThat(result.get(0)).isNotNull();
        }
    }

    @ParameterizedTest(name = "filteredBy{0}_shouldBeEmpty")
    @MethodSource
    void filteredByNotExisting_shouldBeEmpty(String searchDescription, LedgerEntryModel.LedgerEntrySearch search) {

        Collection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(search).getAllNodes();

        assertThat(ledgerEntries)
            .isEmpty();
    }

    private Stream<Arguments> filteredByNotExisting_shouldBeEmpty() {
        String notExistingSearchValue = "not_existing";
        return Stream.of(
            Arguments.of("NotTradedCurrency", byCurrency(notTradedInstrument.getTicker().getBase())),
            Arguments.of("NotExistingReservationRef", byReservationRef(notExistingSearchValue)),
            Arguments.of("NotExistingTransactionId", byTransactionId(notExistingSearchValue))
        );
    }

    @Test
    void pageSize() {

        int pageSize = 10;
        Collection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(
            page(pageSize, null)).getAllNodes();

        assertThat(ledgerEntries)
            .hasSize(pageSize);
    }

    /**
     * total size of ledger entries per simple trade transaction is 10
     * 5 per portfolio + 5 per account:
     * reservation, cash_trade_credit, cash_trade_debit, reservation_release, reservation_release_remaining
     */
    @Test
    void totalSize() {

        TransactionModel.StreetCashTrade transaction = findOneStreetCashTradeTransaction();

        String requestedTransactionId = transaction.id();

        CursorConnection<LedgerEntry> ledgerEntries = restMgmtActor.ledgerEntries().search(
            byTransactionId(requestedTransactionId, 5)
        );

        assertThat(ledgerEntries.pageInfo().totalSize())
            // stabilization note: for some reason may vary between 8 and 10 LEs
            .isGreaterThanOrEqualTo(8)
            .isLessThanOrEqualTo(10);
    }


}

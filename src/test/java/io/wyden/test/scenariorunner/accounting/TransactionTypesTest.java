package io.wyden.test.scenariorunner.accounting;

import io.qameta.allure.Epic;
import io.wyden.test.scenariorunner.extension.infra.OemsHealthObserverExtension;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.SecurityIntegratorExtension;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.List;

import static io.wyden.test.scenariorunner.data.infra.Epics.ACCOUNTING;

@Epic(ACCOUNTING)
@ExtendWith({
    OemsHealthObserverExtension.class,
    SecurityIntegratorExtension.class,
    ClientActorExtension.class
})
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.BOOKING_ENGINE
})
@GraphQL
public class TransactionTypesTest {

    @Test
    void getTransactionTypes(GraphQLActor actor) {
        List<String> transactionTypes = actor.booking().transactionTypes();

        Assertions.assertThat(transactionTypes)
            .containsExactlyInAnyOrder(
                "CLIENT_CASH_TRADE",
                "STREET_CASH_TRADE",
                "CLIENT_ASSET_TRADE",
                "STREET_ASSET_TRADE",
                "DEPOSIT",
                "WITHDRAWAL",
                "PORTFOLIO_CASH_TRANSFER",
                "ACCOUNT_CASH_TRANSFER",
                "SETTLEMENT",
                "FEE");
    }
}

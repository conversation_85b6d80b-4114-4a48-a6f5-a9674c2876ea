package io.wyden.test.scenariorunner.trading.fixspec;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.test.scenariorunner.assertion.trading.ClientResponseSoftAssert;
import io.wyden.test.scenariorunner.data.ErrorMsg;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.Rest;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.trading.TradingTestBase;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

import static io.wyden.test.scenariorunner.data.infra.Epics.STREET_ORDER_MANAGEMENT;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests for part C of FIX Online Specification, Order State Changes:
 *
 * <a href="https://www.fixtrading.org/online-specification/order-state-changes/#c-cancelreplace-quantity-changes-1">Fix Trading Spec</a>
 */
@Epic(STREET_ORDER_MANAGEMENT)
abstract class FIXSpec_C_CancelReplace_Test extends TradingTestBase {

    protected static final String CANCEL_IS_NOT_POSSIBLE_IN_CURRENT_STATE = "Order cancel is not possible in current state";

    /**
     * Scenario C.1.a - request rejected by salesperson variant
     *
     * Order request to venue A, then Cancel it, to trigger "rejected by salesperson" scenario when issuing Cancel/Reject.
     */
    @Test
    void zeroFilledOrder_increaseOrderQty_rejectedBySalesperson(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        client.sendCancel();
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);

        // when
        client.sendCancelReplace(11_000.0);

        // then
        client.receiveCancelRejectAfterCancelReplace();

        // assert
        assertThat(client.cancelRejectOrderStatus()).isEqualTo(ClientOrderStatus.PENDING_CANCEL);
        client.assertCancelRejectOrderOriginalClientId();
        conn.acceptCancel(0);
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_CANCELED, ClientOrderStatus.CANCELED);
    }

    /**
     * Scenario C.1.a - request rejected by exchange variant
     *
     * Order request to venue A, then try to Cancel/Replace it, but it gets rejected by the exchange.
     */
    @Test
    void zeroFilledOrder_increaseOrderQty_rejectedByExchange(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);

        // when
        client.sendCancelReplace(11_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(10_000.0, 0.0, 10_000.0, 0.0);

        // when
        conn.rejectCancel(ErrorMsg.BROKER_OPTION);

        // then
        client.receiveCancelRejectAfterCancelReplace();

        // assert
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.cancelRejectOrderStatus()).isEqualTo(ClientOrderStatus.NEW);
    }

    /**
     * Scenario C.1.a
     *
     * Zero-filled order, cancel/replace request issued to increase order quantity
     */
    @Test
    void zeroFilledOrder_increaseOrderQty_orderExtended(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);

        // when
        client.sendCancelReplace(11_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(10_000.0, 0.0, 10_000.0, 0.0);

        // then
        conn.acceptCancel(0);

        awaitAndVerifyPendingCancelAndCancelledEROfOriginalOrder(client);

        conn.acceptNewOrder();

        // assert
        assertThat(conn.getOrderQuantity()).isEqualTo(11_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_REPLACED, ClientOrderStatus.NEW);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(11000.0, 0.0, 11000.0, 0.0);

        // when
        conn.fillPart("execution-1", 1000.0, 10000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportQuantities(11000.0, 1000.0, 10000.0, 1000.0);

        // when
        conn.fillPart("execution-2", 2000.0, 10_000);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-2");
        client.assertExecutionReportQuantities(11000.0, 3000.0, 8000.0, 2000.0);
    }

    protected void awaitAndVerifyPendingCancelAndCancelledEROfOriginalOrder(ClientSession client) {
        // nothing to do by default
    }

    /**
     * Scenario C.1.b - request rejected by exchange variant
     *
     * Part-filled order, cancel/replace request rejected by exchange
     */
    @Test
    void partFilledOrder_increaseOrderQty_rejectedByExchange(ClientSession client, ConnectorMockSession conn) {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        conn.fillPart("execution-1", 1_000.0, 10_000.0);
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportQuantities(10_000.0, 1000.0, 9_000.0, 1_000.0);

        // when
        client.sendCancelReplace(12_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(10_000.0, 1_000.0, 9_000.0, 0.0);

        // when
        conn.fillPart("execution-2", 100.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-2");
        client.assertExecutionReportClientOrderId(client.getOriginalClientOrderId());
        client.assertExecutionReportQuantities(10_000.0, 1_100.0, 8_900.0, 100.0);

        // when
        conn.rejectCancel(ErrorMsg.BROKER_OPTION);

        // then
        client.receiveCancelRejectAfterCancelReplace();
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.cancelRejectOrderStatus()).isEqualTo(ClientOrderStatus.PARTIALLY_FILLED);
    }

    protected void awaitAndVerifyPendingCancelOfOriginalOrder(ClientSession client) {
        // nothing to do by default
    }

    /**
     * Scenario C.1.b
     *
     * Part-filled order, cancel/replace request issued to increase order quantity, execution occurs whilst order is pending replace
     */
    @Test
    void partFilledOrder_increaseOrderQty_orderExtended(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        conn.fillPart("execution-1", 1000.0, 10_000.0);
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportQuantities(10_000.0, 1_000.0, 9_000.0, 1_000.0);

        // when
        client.sendCancelReplace(12_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(10_000, 1_000, 9_000, 0.0);

        // when
        conn.fillPart("execution-2", 100, 10_000);

        awaitAndVerifyPendingCancelOfOriginalOrder(client);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-2");
        client.assertExecutionReportClientOrderId(client.getOriginalClientOrderId());
        client.assertExecutionReportQuantities(10_000, 1_100, 8_900, 100.0);

        // then
        conn.acceptCancel(1_100);

        awaitAndVerifyOriginalCancelledOrderER(client);

        conn.acceptNewOrder();

        awaitAndVerifyNewOrderER(client);

        // assert
        assertThat(conn.getOrderQuantity()).isEqualTo(10_900.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_REPLACED, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(12_000.0, 1_100.0, 10_900.0, 0.0);

        // when
        conn.fillFull("execution-3", 10_900.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-3");
        client.assertExecutionReportQuantities(12_000.0, 12_000.0, 0.0, 10_900.0);
    }

    protected void awaitAndVerifyNewOrderER(ClientSession client) {
        // nothing to do by default
    }

    // Scenario C.1.c - unsupported. It is optional in FIX specification.

    /**
     * Scenario C.2.a
     *
     * Cancel/replace request (not for quantity change) is rejected as fill has occurred
     */
    @Test
    void filledOrder_unchangedOrderQuantity_replaceRejected(ClientSession client, ConnectorMockSession conn) throws IOException {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        conn.fillPart("execution-1", 1_000.0, 10_000);
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportQuantities(10_000.0, 1_000.0, 9_000.0, 1_000.0);

        // when
        client.sendCancelReplace(10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(10_000.0, 1_000.0, 9_000.0, 0.0);

        // when
        conn.fillFull("execution-2", 9_000.0, 10_000.0);

        awaitAndVerifyPendingCancelOfOriginalOrder(client);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-2");
        client.assertExecutionReportClientOrderId(client.getOriginalClientOrderId());
        client.assertExecutionReportQuantities(10000.0, 10000.0, 0.0, 9000.0);

        conn.rejectCancel(ErrorMsg.BROKER_OPTION);

        // then
        client.receiveCancelRejectAfterCancelReplace();
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.cancelRejectOrderStatus()).isEqualTo(ClientOrderStatus.FILLED);
        // TODO: we should assert Result instead on a new protobuf msg type: https://algotrader.atlassian.net/browse/AC-1357
//        assertThat(client.cancelRejectCxlRejectReason()).isEqualTo(cancelRejectCxlRejectReason());
    }

    protected abstract String cancelRejectCxlRejectReason();

    /**
     * Scenario C.3.a - rejected by trader
     *
     * Cancel/replace request sent whilst execution is being reported - the requested order qty exceeds the cumQty. Order is replaced and filled.
     */
    @Test
    void partiallyFilledOrder_decreaseQtyAboveCumQty_rejectedByExchange(ClientSession client, ConnectorMockSession conn) {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        conn.fillPart("execution-1", 1_000.0, 10_000.0);
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportQuantities(10_000.0, 1_000.0, 9_000.0, 1_000.0);

        // when
        conn.fillPart("execution-2", 500.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-2");
        client.assertExecutionReportClientOrderId(client.getClientOrderId());
        client.assertExecutionReportQuantities(10_000.0, 1_500.0, 8_500.0, 500.0);

        // when
        client.sendCancelReplace(8_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(10_000.0, 1_500.0, 8_500.0, 0.0);

        // when
        conn.fillPart("execution-3", 100.0, 10_000.0);

        awaitAndVerifyPendingCancelOfOriginalOrder(client);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-3");
        client.assertExecutionReportQuantities(10_000.0, 1_600.0, 8_400.0, 100.0);

        // then
        conn.rejectCancel(ErrorMsg.BROKER_OPTION);

        // then
        client.receiveCancelRejectAfterCancelReplace();
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.cancelRejectOrderStatus()).isEqualTo(ClientOrderStatus.PARTIALLY_FILLED);
    }

    /**
     * Scenario C.3.a
     *
     * Cancel/replace request sent whilst execution is being reported - the requested order qty exceeds the cumQty. Order is replaced and filled.
     */
    @Test
    void partiallyFilledOrder_decreaseQtyAboveCumQty_orderFilled(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        conn.fillPart("execution-1", 1_000.0, 10_000.0);
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportQuantities(10_000.0, 1_000.0, 9_000.0, 1_000.0);

        // when
        conn.fillPart("execution-2", 500.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-2");
        client.assertExecutionReportClientOrderId(client.getClientOrderId());
        client.assertExecutionReportQuantities(10000.0, 1500.0, 8500.0, 500.0);

        // when
        client.sendCancelReplace(8000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(10000.0, 1500.0, 8500.0, 0.0);

        // when
        conn.fillPart("execution-3", 100.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PENDING_REPLACE);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-3");
        client.assertExecutionReportClientOrderId(client.getOriginalClientOrderId());
        client.assertExecutionReportQuantities(10_000.0, 1_600.0, 8_400.0, 100.0);

        // then
        conn.acceptCancel(1_600.0);
        conn.acceptNewOrder();

        // assert
        assertThat(conn.getOrderQuantity()).isEqualTo(6_400.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_REPLACED, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(8_000.0, 1_600.0, 6_400.0, 0.0);

        // when
        conn.fillFull("execution-4", 6_400.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-4");
        client.assertExecutionReportQuantities(8_000.0, 8_000.0, 0.0, 6_400.0);
    }

    /**
     * Scenario C.3.b
     *
     * Cancel/replace request sent whilst execution is being reported - the requested order qty equals the cumQty. Order is replaced and filled.
     */
    @Test
    void newOrder_decreaseQtyToCumQty_orderReplacedAndFilled(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        client.assertExecutionReportQuantities(10_000.0, 0.0, 10_000.0, 0.0);

        // when
        conn.fillPart("execution-1", 7_000.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportClientOrderId(client.getClientOrderId());
        client.assertExecutionReportQuantities(10_000.0, 7_000.0, 3_000.0, 7_000.0);

        // when
        client.sendCancelReplace(7_000.0);
//        not required step since there is no cancel sent to exchange
//        conn.acceptCancel(7_000.0);

        awaitAndVerifyOriginalCancelledOrderER(client);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_REPLACED, ClientOrderStatus.FILLED);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(7_000.0, 7_000.0, 0.0, 0.0);
    }

    /**
     * Scenario C.3.c
     *
     * Cancel/replace request sent whilst execution is being reported - the requested order qty is below cumQty. Order is amended to cum qty
     */
    @Test
    void newOrder_decreaseQtyBelowCumQty_orderReplacedAndFilled(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        client.assertExecutionReportQuantities(10_000.0, 0.0, 10_000.0, 0.0);

        // when
        conn.fillPart("execution-1", 8_000.0, 10_000.0);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);
        client.assertExecutionIdNotBlank();
        client.assertVenueExecutionId("execution-1");
        client.assertExecutionReportClientOrderId(client.getClientOrderId());
        client.assertExecutionReportQuantities(10_000.0, 8_000.0, 2_000.0, 8_000.0);

        // when
        client.sendCancelReplace(7_000.0);
//        not required step since there is no cancel sent to exchange
//        conn.acceptCancel(8_000.0);

        awaitAndVerifyOriginalCancelledOrderER(client);

        // then
        client.receiveExecutionReport();

        // assert
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_REPLACED, ClientOrderStatus.FILLED);
        client.assertExecutionReportOrigClOrderId();
        client.assertExecutionReportQuantities(8_000.0, 8_000.0, 0.0, 0.0);
    }

    protected void awaitAndVerifyOriginalCancelledOrderER(ClientSession client) {
        // nothing to verify by default
    }

}

@Fix
class Fix_FIXSpec_C_CancelReplace_Test extends FIXSpec_C_CancelReplace_Test {

    @Override
    protected String cancelRejectCxlRejectReason() {
        return "Order already in pending cancel or pending replace status.";
    }
}

@GraphQL
class GraphQL_FIXSpec_C_CancelReplace_Test extends FIXSpec_C_CancelReplace_Test {

    private static final Logger LOGGER = LoggerFactory.getLogger(GraphQL_FIXSpec_C_CancelReplace_Test.class);

    @Override
    protected String cancelRejectCxlRejectReason() {
        return CANCEL_IS_NOT_POSSIBLE_IN_CURRENT_STATE;
    }

    @Step
    protected void awaitAndVerifyPendingCancelOfOriginalOrder(ClientSession client) {
        LOGGER.info("await and verify GQL orderStates specific behaviour: 2 PENDING_CANCEL original order states");
        ClientRequest originalOrder = client.getOriginalOrder();
        // await pending cancel of original order
        ClientResponse firstPendingCancel = client.receiveExecutionReport();
        verifyOriginalOrderER(originalOrder, firstPendingCancel, ClientOrderStatus.PENDING_CANCEL);
        // await pending cancel of original order
        ClientResponse secondPendingCancel = client.receiveExecutionReport();
        verifyOriginalOrderER(originalOrder, secondPendingCancel, ClientOrderStatus.PENDING_CANCEL);
    }

    @Step
    protected void awaitAndVerifyPendingCancelAndCancelledEROfOriginalOrder(ClientSession client) {
        LOGGER.info("await and verify GQL orderStates specific behaviour: PENDING_CANCEL and CANCELED original order states");
        ClientRequest originalOrder = client.getOriginalOrder();
        ClientResponse firstERAfterCancelReplace = client.receiveExecutionReport();
        verifyOriginalOrderER(originalOrder, firstERAfterCancelReplace, ClientOrderStatus.PENDING_CANCEL);
        ClientResponse secondERAfterCancelReplace = client.receiveExecutionReport();
        verifyOriginalOrderER(originalOrder, secondERAfterCancelReplace, ClientOrderStatus.CANCELED);
    }

    @Step
    protected void awaitAndVerifyOriginalCancelledOrderER(ClientSession client) {
        LOGGER.info("await and verify GQL orderStates specific behaviour: CANCELED original order states");
        ClientRequest originalOrder = client.getOriginalOrder();
        ClientResponse cancelledER = client.receiveExecutionReport();
        verifyOriginalOrderER(originalOrder, cancelledER, ClientOrderStatus.CANCELED);
    }

    @Step
    private void verifyOriginalOrderER(ClientRequest originalOrder, ClientResponse clientResponse, ClientOrderStatus expectedStatus) {
        ClientResponseSoftAssert.assertThat(clientResponse)
            .clOrderIdIs(originalOrder.getClOrderId())
            .sideIs(originalOrder.getSide())
            .orderQtyIs(Double.parseDouble(originalOrder.getQuantity()))
            .tifIs(originalOrder.getTif())
            .instrumentIdIs(originalOrder.getInstrumentId())
            .portfolioIdIs(originalOrder.getPortfolioId())
            .clientIdIs(originalOrder.getClientId())
            .orderStatusIs(expectedStatus)
            .assertAll();
    }

    @Override
    @Step
    protected void awaitAndVerifyNewOrderER(ClientSession client) {
        // new ER
        ClientRequest newOrder = client.getOrder();
        ClientResponse newER = client.receiveExecutionReport();
        ClientResponseSoftAssert.assertThat(newER)
            .clOrderIdIs(newOrder.getClOrderId())
            .sideIs(newOrder.getSide())
            .orderQtyIs(Double.parseDouble(newOrder.getQuantity()))
            .tifIs(newOrder.getTif())
            .instrumentIdIs(newOrder.getInstrumentId())
            .portfolioIdIs(newOrder.getPortfolioId())
            .clientIdIs(newOrder.getClientId())
            .orderStatusIs(ClientOrderStatus.NEW)
            .assertAll();
    }

    @Override
    @Disabled("Non deterministic test behaviour with GQL. Pending_Cancel order state is not guaranteed to come before Pending_Replace")
    void partiallyFilledOrder_decreaseQtyAboveCumQty_orderFilled(ClientSession client, ConnectorMockSession conn) throws Exception {

    }

    @Override
    @Disabled("Non deterministic test behaviour with GQL. Pending_Cancel order state is not guaranteed to come before Pending_Replace")
    void partFilledOrder_increaseOrderQty_rejectedByExchange(ClientSession client, ConnectorMockSession conn) {

    }

    @Override
    @Disabled("Non deterministic test behaviour with GQL. Pending_Cancel order state is not guaranteed to come before Pending_Replace")
    void partFilledOrder_increaseOrderQty_orderExtended(ClientSession client, ConnectorMockSession conn) {

    }

    @Override
    @Disabled("Non deterministic test behaviour with GQL. Partially_filled order state is not guaranteed to come before Pending_Replace")
    void partiallyFilledOrder_decreaseQtyAboveCumQty_rejectedByExchange(ClientSession client, ConnectorMockSession conn) {

    }

}

@Rest
class Rest_FIXSpec_C_CancelReplace_Test extends FIXSpec_C_CancelReplace_Test {

    @Override
    protected String cancelRejectCxlRejectReason() {
        return CANCEL_IS_NOT_POSSIBLE_IN_CURRENT_STATE;
    }

}

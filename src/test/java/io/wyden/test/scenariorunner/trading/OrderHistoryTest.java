package io.wyden.test.scenariorunner.trading;

import io.qameta.allure.Epic;
import io.wyden.apiserver.rest.apiui.SharedModel;
import io.wyden.apiserver.rest.apiui.SharedModel.SortingOrder;
import io.wyden.apiserver.rest.orderhistory.model.OrderStateResponse;
import io.wyden.apiserver.rest.orderhistory.model.OrderStateSearchInput;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDesc;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.integration.gqlclient.searchinput.OrderStateSearchInputs;
import io.wyden.test.scenariorunner.integration.service.Service;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

import static io.wyden.test.scenariorunner.data.infra.Epics.STREET_ORDER_MANAGEMENT;
import static org.assertj.core.api.Assertions.assertThat;

@Epic(STREET_ORDER_MANAGEMENT)
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.ORDER_COLLIDER,
    Service.ORDER_GATEWAY,
    Service.REFERENCE_DATA,
    Service.CONNECTOR_WRAPPER_MOCK,
    Service.REST_API_SERVER,
    Service.RISK_ENGINE,
    Service.STORAGE
})
public class OrderHistoryTest extends HistoryBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderHistoryTest.class);

    @Test
    void filteringByTradedPortfolio() {

        Collection<OrderStateResponse> portfolioOrderStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byPortfolio(portfolio1)
        ).getAllNodes();

        assertThat(portfolioOrderStates)
            .extracting(OrderStateResponse::getPortfolioId)
            .containsOnly(portfolio1);
    }

    @Test
    void filteringByTradedAccount() {

        Collection<OrderStateResponse> accountOrderStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byAccount(account1)
        ).getAllNodes();

        assertThat(accountOrderStates)
            .flatMap(OrderStateResponse::getVenueAccountDescs)
            .map(VenueAccountDesc::name)
            .containsOnly(account1);
    }

    @Test
    void filteringByOneTradedInstrumentId() {

        Collection<OrderStateResponse> singleInstrumentStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byInstrumentIds(tradedInstrument2.getName())).getAllNodes();

        assertThat(singleInstrumentStates)
            .extracting(state -> state.getInstrument().instrumentIdentifiers().instrumentId())
            .containsOnly(tradedInstrument2.getName());
    }

    @Test
    void filteringBySeveralTradedInstrumentIds() {

        Collection<OrderStateResponse> severalInstrumentsStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byInstrumentIds(tradedInstrument1.getName(), tradedInstrument2.getName())).getAllNodes();

        assertThat(severalInstrumentsStates)
            .extracting(state -> state.getInstrument().instrumentIdentifiers().instrumentId())
            .containsOnly(tradedInstrument1.getName(), tradedInstrument2.getName());
    }

    @Test
    void filteringByOneExistingOrderStatus() {

        Collection<OrderStateResponse> filledOrderStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byOrderStatuses(SharedModel.OrderStatus.FILLED.name())).getAllNodes();

        assertThat(filledOrderStates)
            .extracting(OrderStateResponse::getOrderStatus)
            .containsOnly(SharedModel.OrderStatus.FILLED);
    }

    @Test
    void filteringBySeveralExistingOrderStatuses() {

        Collection<OrderStateResponse> filledAndCanceledOrderStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byOrderStatuses(SharedModel.OrderStatus.FILLED.name(), SharedModel.OrderStatus.CANCELED.name())).getAllNodes();

        assertThat(filledAndCanceledOrderStates)
            .extracting(OrderStateResponse::getOrderStatus)
            .containsOnly(SharedModel.OrderStatus.FILLED, SharedModel.OrderStatus.CANCELED);
    }

    @Test
    void whenFilteredFromYesterdayByCreatedAt_thenOnlyOrderStatesCreatedLaterThenYesterdayReturned() {

        long yesterday = ZonedDateTime.now().minus(1, ChronoUnit.DAYS).toInstant().toEpochMilli();
        Collection<OrderStateResponse> createdFromYesterday = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.fromCreatedAt(yesterday)).getAllNodes();

        assertThat(createdFromYesterday)
            .extracting(state -> Long.parseLong(state.getCreatedAt()))
            .allSatisfy(createdAt -> assertThat(createdAt)
                .isGreaterThanOrEqualTo(yesterday));
    }

    @Test
    void whenFilteredToDateByUpdatedAt_thenOnlyOrderStatesUpdatedEqualOrEarlierThenDateReturned() {

        Collection<OrderStateResponse> sortedOrders = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.firstNRecordsInSortOrder(50, SortingOrder.DESC)).getAllNodes();

        ArrayList<OrderStateResponse> sortedOrdersList = new ArrayList<>(sortedOrders);

        // pick 2nd newest order updatedAt
        int latestOrderIndex = 1;
        String orderUpdatedAt = sortedOrdersList.get(latestOrderIndex).getUpdatedAt();
        // known issue with not enough accuracy from outside API https://algotrader.atlassian.net/browse/AC-2563
        long updatedAtPlus1Ms = Long.parseLong(orderUpdatedAt) + 1;
        String to = String.valueOf(updatedAtPlus1Ms);

        Collection<OrderStateResponse> updatedFromEpoch = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.toUpdatedAt(to)).getAllNodes();

        assertThat(updatedFromEpoch)
            .extracting(state -> Long.parseLong(state.getUpdatedAt()))
            .allSatisfy(updatedAt -> assertThat(updatedAt)
                .isLessThanOrEqualTo(updatedAtPlus1Ms));
    }

    @Test
    void whenFilteredToNowByCreatedAt_thenOnlyOrderStatesCreatedEarlierThenNowReturned() {

        long now = ZonedDateTime.now().toInstant().toEpochMilli();
        Collection<OrderStateResponse> createdToNow = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.toCreatedAt(now)).getAllNodes();

        assertThat(createdToNow)
            .extracting(state -> Long.parseLong(state.getCreatedAt()))
            .allSatisfy(createdAt -> assertThat(createdAt)
                .isLessThanOrEqualTo(now));
    }

    @Test
    void filteringByExistingOrderId() {

        String orderId = mockOrderPrefillExtension.oneLastOrderResponse().getOrderId();
        Collection<OrderStateResponse> orderIdStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byOrderId(orderId)).getAllNodes();

        assertThat(orderIdStates)
            .hasSize(1)
            .extracting(OrderStateResponse::getOrderId)
            .containsExactly(orderId);
    }

    @ParameterizedTest(name = "whenSortedIn{0}Order_thenOrderStatesAreSorted{0}ByUpdatedAt")
    @MethodSource
    void whenSorted_thenOrderStatesAreSortedByUpdatedAt(SortingOrder sortOrder, Comparator<OrderStateResponse> comparator) {

        int pageSize = 50;
        List<OrderStateResponse> all = (List<OrderStateResponse>) gqlActor.orderState().historyPage(
            OrderStateSearchInputs.firstNRecordsInSortOrder(pageSize, sortOrder)).getAllNodes();

        assertThat(all)
            .isSortedAccordingTo(comparator);
    }

    private static Stream<Arguments> whenSorted_thenOrderStatesAreSortedByUpdatedAt() {
        return Stream.of(
            Arguments.of(SortingOrder.ASC, descByUpdatedAt().reversed()),
            Arguments.of(SortingOrder.DESC, descByUpdatedAt())
        );
    }

    private static Comparator<OrderStateResponse> descByUpdatedAt() {
        return (o1, o2) -> StringUtils.compare(o2.getUpdatedAt(), o1.getUpdatedAt());
    }

    @ParameterizedTest(name = "whenFilteredBy{0}_thenNoOrderStatesReturned")
    @MethodSource
    void whenFilteredByNotExisting_thenNoOrderStatesReturned(String searchDescription, OrderStateSearchInput searchInput) {

        Collection<OrderStateResponse> states = gqlActor.orderState().historyPage(
            searchInput).getAllNodes();

        assertThat(states)
            .isEmpty();
    }

    private Stream<Arguments> whenFilteredByNotExisting_thenNoOrderStatesReturned() {
        String notExistingSearchValue = "fake";
        long tomorrow = ZonedDateTime.now().plus(1, ChronoUnit.DAYS).toInstant().toEpochMilli();
        long oneYearAgo = ZonedDateTime.now().minus(1, ChronoUnit.YEARS).toInstant().toEpochMilli();
        return Stream.of(
            Arguments.of("NotTradedPortfolioId", OrderStateSearchInputs.byPortfolio(notTradedPortfolio)),
            // no dynamic portfolio permissions
//            Arguments.of("NotTradedTarget", OrderStateSearchInputs.byTarget(notTradedAccount)),
            Arguments.of("NotTradedInstrumentId", OrderStateSearchInputs.byInstrumentIds(notTradedInstrument.getName())),
            Arguments.of("NotExistingOrderStatus", OrderStateSearchInputs.byOrderStatuses(notExistingSearchValue)),
            Arguments.of("NotExistingOrderId", OrderStateSearchInputs.byOrderId(notExistingSearchValue)),
            Arguments.of("CreatedAtToOneYearAgo", OrderStateSearchInputs.toCreatedAt(oneYearAgo)),
            Arguments.of("CreatedAtFromTomorrow", OrderStateSearchInputs.fromCreatedAt(tomorrow))
        );
    }

    @Test
    void filteringByPortfolioAndInstrumentIdAndFromDate_ANDBehaviourBetweenDifferentPredicateTypes() {

        Collection<OrderStateResponse> sortedStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.firstNRecordsInSortOrder(4, SortingOrder.DESC)).getAllNodes();

        ArrayList<OrderStateResponse> sortedStatesList = new ArrayList<>(sortedStates);

        int orderIndex = 3;
        String orderCreatedAt = sortedStatesList.get(orderIndex).getCreatedAt();
        long createdAt = Long.parseLong(orderCreatedAt) - 1;

        LOGGER.info("Picked 4th newest order createdAt: " + orderCreatedAt);

        Collection<OrderStateResponse> orderStates = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byPortfolioIdAndInstrumentIdAndFromDate(portfolio1, tradedInstrument1.getName(), createdAt)
        ).getAllNodes();

        assertThat(orderStates)
            .isNotEmpty()
            .allSatisfy(o -> assertThat(Long.parseLong(o.getCreatedAt()))
                .isGreaterThanOrEqualTo(createdAt))
            .extracting(OrderStateResponse::getPortfolioId, state -> state.getInstrument().instrumentIdentifiers().instrumentId())
            .containsExactly(new Tuple(portfolio1, tradedInstrument1.getName()));
    }

    @Test
    void filteringByPortfolioAndAccount_ANDBehaviourBetweenSimplePredicates() {

        String orderId = mockOrderPrefillExtension.oneLastOrderResponse().getOrderId();
        Collection<OrderStateResponse> states = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byPortfolioAndOrderId(portfolio1, orderId)
        ).getAllNodes();

        assertThat(states)
            .hasSize(1)
            .extracting(OrderStateResponse::getPortfolioId, OrderStateResponse::getOrderId)
            .containsExactly(new Tuple(portfolio1, orderId));
    }

    @Test
    void filteringByInstrumentIdAndOrderStatus_ANDBehaviourBetweenCollectionPredicates() {
        Collection<OrderStateResponse> states = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.byInstrumentIdAndOrderStatus(tradedInstrument1.getName(), SharedModel.OrderStatus.CANCELED.name())
        ).getAllNodes();

        assertThat(states)
            .isNotEmpty()
            .extracting(state -> state.getInstrument().instrumentIdentifiers().instrumentId(), OrderStateResponse::getOrderStatus)
            .containsOnly(new Tuple(tradedInstrument1.getName(), SharedModel.OrderStatus.CANCELED));
    }

    @Test
    void filteringByFromAndToDates_ANDBehaviourBetweenDatePredicates() {
        OrderStateResponse latestState = gqlActor.orderState().historyPage(OrderStateSearchInputs.firstNRecordsInSortOrder(1, SortingOrder.DESC))
            .getAllNodes().iterator().next();

        String from = latestState.getCreatedAt();
        // known issue with not enough accuracy from outside API https://algotrader.atlassian.net/browse/AC-2563
        long updatedAtPlus1Ms = Long.parseLong(latestState.getUpdatedAt()) + 1;
        String to = String.valueOf(updatedAtPlus1Ms);

        Collection<OrderStateResponse> states = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.fromCreatedAtToUpdatedAt(from, to)
        ).getAllNodes();

        assertThat(states)
            // several orders may be in result when tests are running in parallel
            .hasSizeGreaterThanOrEqualTo(1)
            .extracting(OrderStateResponse::getOrderId)
            .contains(latestState.getOrderId());
    }

    @Test
    void pageSize() {

        int pageSize = 3;
        Collection<OrderStateResponse> states = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.firstNRecords(pageSize, null)).getAllNodes();

        assertThat(states)
            .hasSize(pageSize);
    }

    //TODO rework
    @Disabled("Administrator has access to all order states")
    @Test
    void pageSizeAfterCursor() {
        String endCursor = newest4States()
            .pageInfo().endCursor();

        LOGGER.info("Picked 4th newest state with endCursor: {}", endCursor);

        List<OrderStateResponse> list = new ArrayList<>(newest5States().getAllNodes());
        String nextStateUpdatedAt = list
            .get(4).getUpdatedAt();

        LOGGER.info("Expected next 5th state with updatedAt: " + nextStateUpdatedAt);

        int pageSize = 1;
        Collection<OrderStateResponse> states = gqlActor.orderState().historyPage(
            OrderStateSearchInputs.firstNRecords(pageSize, endCursor)).getAllNodes();

        assertThat(states)
            .hasSize(pageSize)
            .extracting(OrderStateResponse::getUpdatedAt)
            .containsExactly(nextStateUpdatedAt);
    }

    private PaginationModel.CursorConnection<OrderStateResponse> newest5States() {
        return gqlActor.orderState().historyPage(OrderStateSearchInputs.firstNRecordsInSortOrder(5, SortingOrder.DESC));
    }

    private PaginationModel.CursorConnection<OrderStateResponse> newest4States() {
        return gqlActor.orderState().historyPage(
            OrderStateSearchInputs.firstNRecordsInSortOrder(4, SortingOrder.DESC));
    }

}

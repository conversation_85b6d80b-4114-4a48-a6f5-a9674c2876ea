query Transactions($search: TransactionSearchInput!) {
  transactions(search: $search) {
    edges {
      node {
        ... on ClientCashTrade {
          orderId
          executionId
          venueExecutionId
          uuid
          updatedAt
          dateTime
          fee
          feeCurrency
          description
          quantity
          price
          currency
          intOrderId
          extOrderId
          baseCurrency
          portfolioId
          portfolioName
          counterPortfolioId
          counterPortfolioName
          rootExecution {
            orderId
            executionId
          }
          settled
          settledDateTime
        }
        ... on StreetAssetTrade {
          orderId
          executionId
          venueExecutionId
          uuid
          updatedAt
          dateTime
          fee
          feeCurrency
          description
          quantity
          price
          currency
          intOrderId
          extOrderId
          instrument {
            ...InstrumentContent
          }
          portfolioId
          portfolioName
          venueAccount
          venueAccountName
          rootOrderId
          settled
          settledDateTime
        }
        ... on ClientAssetTrade {
          orderId
          uuid
          updatedAt
          dateTime
          fee
          feeCurrency
          description
          quantity
          price
          currency
          intOrderId
          extOrderId
          instrument {
            ...InstrumentContent
          }
          portfolioId
          portfolioName
          counterPortfolioId
          counterPortfolioName
          rootExecution {
            orderId
            executionId
          }
          settled
          settledDateTime
        }
        ... on StreetCashTrade {
          orderId
          executionId
          venueExecutionId
          uuid
          updatedAt
          dateTime
          fee
          feeCurrency
          description
          quantity
          price
          currency
          intOrderId
          extOrderId
          baseCurrency
          portfolioId
          portfolioName
          venueAccount
          venueAccountName
          rootOrderId
          settled
          settledDateTime
        }
        ... on Withdrawal {
          uuid
          updatedAt
          dateTime
          executionId
          venueExecutionId
          description
          quantity
          currency
          portfolioId
          portfolioName
          account
          accountName
          settled
          settledDateTime
          feeAccountId
          feeAccountName
          feePortfolioId
          feePortfolioName
        }
        ... on Deposit {
          uuid
          updatedAt
          dateTime
          executionId
          venueExecutionId
          description
          quantity
          currency
          portfolioId
          portfolioName
          account
          accountName
          settled
          settledDateTime
          feeAccountId
          feeAccountName
          feePortfolioId
          feePortfolioName
        }
        ... on AccountCashTransfer {
          uuid
          updatedAt
          dateTime
          executionId
          venueExecutionId
          description
          quantity
          currency
          sourceAccountId
          sourceAccountName
          targetAccountId
          targetAccountName
          settled
          settledDateTime
          feeAccountId
          feeAccountName
          feePortfolioId
          feePortfolioName
        }
        ... on PortfolioCashTransfer {
          uuid
          updatedAt
          dateTime
          executionId
          venueExecutionId
          description
          quantity
          currency
          sourcePortfolioId
          sourcePortfolioName
          targetPortfolioId
          targetPortfolioName
          settled
          settledDateTime
          feePortfolioId
          feePortfolioName
        }
        ... on Settlement {
          uuid
          updatedAt
          dateTime
          description
          settledTransactionIds
        }
        ...on Fee {
          dateTime
          uuid
          updatedAt
          executionId
          venueExecutionId
          description
          quantity
          currency
          portfolioId
          portfolioName
          account
          accountName
          settled
          settledDateTime
          feeOrderId: orderId
          parentOrderId
          underlyingExecutionId
          rootExecution {
            orderId
            executionId
          }
        }
      }
      cursor
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
  transactionTypes
}

import { useTradingInstrument } from '@wyden/hooks/useTradingInstrument';
import { BaseWidgetData } from '@wyden/hooks/useUserData';
import { useRef, useState } from 'react';
import { useAppSelector } from '../../redux-hooks';
import { InstrumentResponse } from '../../services/graphql/generated/graphql';
import { selectBiggestMaxTotalBidOrAskSum } from '../order-book/OrderBook/OrderBook/orderbookSlice';
import {
  ORDERBOOK_HORIZONTAL_VERTICAL_BIGGEST_NUMBER_BREAKPOINT,
  ORDERBOOK_HORIZONTAL_VERTICAL_WIDGET_WIDTH_BREAKPOINT,
} from '../order-book/constants';
import { useWidget } from '../widgets-renderer/widget/hooks/useWidget';
import { CustomOBSubscribeParameters, DynamicOrderBook } from './OrderBook/OrderBook';

export type WatchlistElement = {
  instrument: InstrumentResponse;
  venueAccount: string;
};

const calculateRowsCount = (
  widgetElement: HTMLElement | null,
  newObMode: 'HORIZONTAL' | 'VERTICAL',
): number => {
  if (widgetElement) {
    return newObMode === 'VERTICAL'
      ? Math.round((widgetElement.clientHeight - 170) / 42)
      : Math.round((widgetElement.clientHeight - 160) / 20);
  } else {
    return 0;
  }
};
type Props = {
  visualizationFlavor: 'VERTICAL' | 'HORIZONTAL' | 'AUTO';
  customSubscribeParameters?: CustomOBSubscribeParameters;
  customRowCounts?: number;
};

export const OrderBookContent = ({
  visualizationFlavor,
  customSubscribeParameters,
  customRowCounts,
}: Props) => {
  const orderBookContainer = useRef<HTMLDivElement>(null);
  const { widgetData } = useWidget<BaseWidgetData>('BASE');
  const widgetDataId = widgetData.id;
  const { instrument } = useTradingInstrument();

  const widgetElement = document.getElementById(widgetDataId);
  const maxTotalBidOrAskSum = useAppSelector(selectBiggestMaxTotalBidOrAskSum);
  const obMode = determineOBMode(maxTotalBidOrAskSum, orderBookContainer, visualizationFlavor);
  const [, setOBMode] = useState<'HORIZONTAL' | 'VERTICAL'>(obMode);
  const [obReloading, setObReloading] = useState(false);

  // This handles browser resizing
  window.onresize = () => {
    setOBMode(determineOBMode(maxTotalBidOrAskSum, orderBookContainer, visualizationFlavor));
  };

  const triggerObReload = () => {
    setObReloading(true);
    setTimeout(() => {
      setObReloading(false);
    }, 100);
  };

  return (
    <div ref={orderBookContainer}>
      {
        // It is to destroy ob and reestablish subscribe hook again after error
        !obReloading && (
          <DynamicOrderBook
            instrument={instrument || null}
            obMode={obMode}
            triggerObReload={triggerObReload}
            customSubscribeParameters={customSubscribeParameters}
            rowCounts={customRowCounts || calculateRowsCount(widgetElement, obMode)}
          />
        )
      }
    </div>
  );
};

function determineOBMode(
  maxTotalBidOrAskSum: number,
  obRect: React.RefObject<HTMLDivElement>,
  visualizationFlavor: 'VERTICAL' | 'HORIZONTAL' | 'AUTO',
) {
  const orderBookWrapperRect = obRect.current?.getBoundingClientRect();
  const noSpaceForHorizontalMode =
    (orderBookWrapperRect?.width &&
      orderBookWrapperRect?.width < ORDERBOOK_HORIZONTAL_VERTICAL_WIDGET_WIDTH_BREAKPOINT) ||
    maxTotalBidOrAskSum > ORDERBOOK_HORIZONTAL_VERTICAL_BIGGEST_NUMBER_BREAKPOINT;
  const verticalOrderBook =
    (noSpaceForHorizontalMode && visualizationFlavor === 'AUTO') ||
    visualizationFlavor === 'VERTICAL';
  return verticalOrderBook ? 'VERTICAL' : 'HORIZONTAL';
}

import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { Label } from '@ui/Typography/Label';
import { WydenGrid } from '@wyden/features/grid/WydenGrid';
import { formatNumber } from '@wyden/helpers/formatNumber';
import { HedgeResult } from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import { GetRowIdParams } from 'ag-grid-community';
import { useTranslation } from 'react-i18next';
import { Side } from '../../services/graphql/generated/graphql';
import { SideColoredLabel } from '../orders/SideRenderer';
import { VenueIcon } from '../venue-account/VenueIcon';
import { orderDetailsHedgeColumnsDefinitions } from './hedgeColumnDefinitions';
import { v4 as uuidv4 } from 'uuid';

export type HedgeResultWithCurrency = HedgeResult & {
  topOfBookPrice?: number;
  currency?: string;
  id: string;
};

export const getHedgeTableRowData = (hedgeResult: HedgeResult): HedgeResultWithCurrency[] => {
  const primarySymbolMarketPrice =
    hedgeResult?.hedgeOrderSide === Side.Buy
      ? hedgeResult?.primarySymbolQuote?.marketAskPrice
      : hedgeResult?.primarySymbolQuote?.marketBidPrice;

  const secondarySymbolMarketPrice =
    hedgeResult?.hedgeOrderSide === Side.Buy
      ? hedgeResult?.secondarySymbolQuote?.marketAskPrice
      : hedgeResult?.secondarySymbolQuote?.marketBidPrice;

  const valuesInPrimarySymbolCurrency: HedgeResultWithCurrency = {
    topOfBookPrice: primarySymbolMarketPrice || undefined,
    // Estimated Price is expressed in the quote curr of the purchased instrument. In triangulation, it is directly estimated price of secondarySymbolQoute
    estimatedPrice: hedgeResult?.secondarySymbolQuote
      ? (hedgeResult?.estimatedPrice ?? 0) / (secondarySymbolMarketPrice ?? 1)
      : hedgeResult.estimatedPrice ?? 0,
    executionPrice: hedgeResult?.executionPrice ?? 0,
    hedgeOrderLimitPrice: hedgeResult?.hedgeOrderLimitPrice ?? 0,
    currency: hedgeResult?.primarySymbolQuote?.instrument?.baseInstrument?.quoteCurrency ?? '',
    timestamp: hedgeResult?.timestamp ?? new Date().toISOString(),
    clobOrderId: hedgeResult.clobOrderId,
    clobRootOrderId: hedgeResult.clobRootOrderId,
    hedgeOrderId: hedgeResult.hedgeOrderId,
    hedgeRootOrderId: hedgeResult.hedgeRootOrderId,
    hedgeOrderQuantity: hedgeResult.hedgeOrderQuantity,
    hedgeOrderAmount: hedgeResult.hedgeOrderAmount,
    hedgeOrderSide: hedgeResult.hedgeOrderSide,
    hedgingVenue: hedgeResult.hedgingVenue,
    matchId: hedgeResult.matchId,
    success: hedgeResult.success,
    id: uuidv4(),
  };

  const valuesInSecondarySymbolCurrency: HedgeResultWithCurrency = {
    topOfBookPrice: (primarySymbolMarketPrice ?? 0) * (secondarySymbolMarketPrice ?? 0),
    estimatedPrice: hedgeResult?.estimatedPrice ?? 0,
    executionPrice: (hedgeResult?.executionPrice ?? 0) * (secondarySymbolMarketPrice ?? 1),
    hedgeOrderLimitPrice:
      (hedgeResult?.hedgeOrderLimitPrice ?? 0) * (secondarySymbolMarketPrice ?? 1),
    currency: hedgeResult?.secondarySymbolQuote?.instrument?.baseInstrument?.quoteCurrency ?? '',
    timestamp: hedgeResult?.timestamp ?? new Date().toISOString(),
    clobOrderId: hedgeResult.clobOrderId ?? '',
    clobRootOrderId: hedgeResult.clobRootOrderId ?? '',
    hedgeOrderId: hedgeResult.hedgeOrderId ?? '',
    hedgeRootOrderId: hedgeResult.hedgeRootOrderId ?? '',
    hedgeOrderQuantity: hedgeResult.hedgeOrderQuantity,
    hedgeOrderAmount: hedgeResult.hedgeOrderAmount,
    hedgeOrderSide: hedgeResult.hedgeOrderSide,
    hedgingVenue: hedgeResult.hedgingVenue,
    matchId: hedgeResult.matchId,
    success: hedgeResult.success,
    id: uuidv4(),
  };

  const tableRowsData = [valuesInPrimarySymbolCurrency];

  if (hedgeResult?.secondarySymbolQuote) {
    tableRowsData.push(valuesInSecondarySymbolCurrency);
  }

  return tableRowsData;
};

const getRowId = (params: GetRowIdParams) => params.data.id;

export const Hedge = ({ hedgeResult }: { hedgeResult: HedgeResult }) => {
  const { t } = useTranslation();
  const hedgeTableRowData = getHedgeTableRowData(hedgeResult);

  const primarySymbolMarketPrice =
    hedgeResult?.hedgeOrderSide === Side.Buy
      ? hedgeResult?.primarySymbolQuote?.marketAskPrice
      : hedgeResult?.primarySymbolQuote?.marketBidPrice;

  const secondarySymbolMarketPrice =
    hedgeResult?.hedgeOrderSide === Side.Buy
      ? hedgeResult?.secondarySymbolQuote?.marketAskPrice
      : hedgeResult?.secondarySymbolQuote?.marketBidPrice;

  const sortedOrderDetailsHedgeColumnsDefinitions =
    hedgeResult?.hedgeOrderSide === Side.Buy
      ? orderDetailsHedgeColumnsDefinitions
      : orderDetailsHedgeColumnsDefinitions.reverse();

  const primarySymbolBaseCurrency =
    hedgeResult?.primarySymbolQuote?.instrument?.forexSpotProperties?.baseCurrency;
  const primarySymbolQuoteCurrency =
    hedgeResult?.primarySymbolQuote?.instrument?.baseInstrument?.quoteCurrency;
  const secondarySymolQouteCurrency =
    hedgeResult?.secondarySymbolQuote?.instrument?.baseInstrument?.quoteCurrency;

  return (
    <>
      <StyledHedgeContent $success={hedgeResult.success}>
        <StyledLeftColumnPlaceholder></StyledLeftColumnPlaceholder>
        <StyledCenterDiv>
          <div>
            <StyledSecondaryColorSpan>
              {t('ordersHistory.orderDetails.hedge')} {!hedgeResult.success && t('common.failed')} (
              <u>
                {t('ordersHistory.orderDetails.order')}: {hedgeResult?.hedgeOrderId}
              </u>
              )
            </StyledSecondaryColorSpan>
            <div>
              {formatNumber(hedgeResult?.hedgeOrderQuantity ?? 0, 'BUILT_IN', {
                code: primarySymbolBaseCurrency ? primarySymbolBaseCurrency : '',
                dispCurrencyCode: true,
              })}
              <StyledSecondaryColorSpan>
                {' '}
                {t('ordersHistory.orderDetails.for')}{' '}
              </StyledSecondaryColorSpan>
              {formatNumber(hedgeResult?.hedgeOrderAmount ?? 0, 'BUILT_IN', {
                code: primarySymbolQuoteCurrency ? primarySymbolQuoteCurrency : '',
                dispCurrencyCode: true,
              })}{' '}
              {Boolean(
                hedgeResult?.hedgeOrderAmountInClobQuoteCurrency && secondarySymolQouteCurrency,
              ) && (
                <>
                  (
                  {formatNumber(hedgeResult?.hedgeOrderAmountInClobQuoteCurrency ?? 0, 'BUILT_IN', {
                    code: secondarySymolQouteCurrency ? secondarySymolQouteCurrency : '',
                    dispCurrencyCode: true,
                  })}
                  )
                </>
              )}
            </div>
            <StyledSecondaryColorSpan>@ </StyledSecondaryColorSpan>
            {formatNumber(primarySymbolMarketPrice ?? 0, 'BUILT_IN', {
              code: primarySymbolQuoteCurrency ? primarySymbolQuoteCurrency : '',
              dispCurrencyCode: true,
            })}{' '}
            {Boolean(secondarySymbolMarketPrice && secondarySymolQouteCurrency) && (
              <>
                (
                {formatNumber(secondarySymbolMarketPrice ?? 0, 'BUILT_IN', {
                  code: secondarySymolQouteCurrency ? secondarySymolQouteCurrency : '',
                  dispCurrencyCode: true,
                })}
                )
              </>
            )}
          </div>
        </StyledCenterDiv>
        <StyledHedgeRightSection>
          <StyledLabel>
            <StyledWeakColorSpan>{t('ordersHistory.orderDetails.market')}</StyledWeakColorSpan>
          </StyledLabel>
          <StyledVenueLabel>
            <StyledSecondaryColorSpan>
              <VenueIcon venue={hedgeResult?.hedgingVenue} />
            </StyledSecondaryColorSpan>
          </StyledVenueLabel>
          <StyledLabel>
            <span>
              {hedgeResult?.hedgeOrderSide && (
                <SideColoredLabel side={hedgeResult?.hedgeOrderSide} />
              )}
            </span>
          </StyledLabel>
          <StyledLabel>
            <StyledInfoColorSpan>
              {hedgeResult?.secondarySymbolQuote?.instrument?.baseInstrument?.symbol}(
              {t('ordersHistory.orderDetails.breakEven')}): {hedgeResult?.breakEvenFxRate ?? 'N/A'}
            </StyledInfoColorSpan>
          </StyledLabel>
        </StyledHedgeRightSection>
      </StyledHedgeContent>

      <WydenGrid
        rowData={hedgeTableRowData}
        columnDefs={sortedOrderDetailsHedgeColumnsDefinitions}
        getRowId={getRowId}
      />
    </>
  );
};

const StyledHedgeContent = styled('div')<{
  $success: boolean;
}>`
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 11px;
  background-color: ${({ $success, theme }) =>
    $success
      ? color[theme.palette.mode].fillsSurfaceSurfaceTertiary
      : color[theme.palette.mode].fillsSemanticFillErrorBg};
`;

const StyledCenterDiv = styled('div')`
  width: 400px;
  display: flex;
  text-align: center;
  justify-content: center;
  line-height: 24px;
  font-weight: 500;
`;

const StyledHedgeRightSection = styled('div')`
  width: 200px;
  display: flex;
  text-align: right;
  justify-content: flex-end;
  flex-direction: column;
  line-height: 24px;
  gap: ${getSpacing(1)};
`;

const StyledLeftColumnPlaceholder = styled('div')`
  width: 200px;
`;

const StyledLabel = styled(Label)`
  font-size: 11px;
`;

const StyledVenueLabel = styled(Label)`
  display: flex;
  align-items: left;
  justify-content: flex-end;
  gap: 4px;
  font-size: 11px;
`;

const StyledSecondaryColorSpan = styled('span')`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const StyledWeakColorSpan = styled('span')`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;

const StyledInfoColorSpan = styled('span')`
  color: ${({ theme }) => color[theme.palette.mode].textSemanticTextInfoLight};
`;

import {
  DateRendererForMsTimestamp,
  DateRendererToGoWithValueGetter,
  ISODateValueGetter,
} from '@wyden/features/grid/DateRenderer';
import {
  buildObjectKeyValidator,
  getCurrencyBasedMoneyField,
  getDateColumnName,
  getGenericField,
  getGenericNumberField,
} from '@wyden/features/grid/utils';
import i18n from '@wyden/i18n';
import {
  ClientAssetTrade,
  ClientCashTrade,
  StreetAssetTrade,
  StreetCashTrade,
  Withdrawal,
  Deposit,
  Settlement,
  AccountCashTransfer,
  PortfolioCashTransfer,
  Fee,
} from '@wyden/services/graphql/generated/graphql';
import { ColDef } from 'ag-grid-community';
import { ActionsRenderer } from './TransactionActionsRenderer';
import { formatTransactionTypeByTypename } from '@wyden/hooks/useEnumFormatters';

export type Transaction = ClientAssetTrade &
  ClientCashTrade &
  StreetAssetTrade &
  StreetCashTrade &
  AccountCashTransfer &
  PortfolioCashTransfer &
  Withdrawal &
  Settlement &
  Deposit &
  Fee;

export const validKey = buildObjectKeyValidator<Transaction>();
export const orderId = validKey('orderId');
export const currency = validKey('currency');
export const description = validKey('description');
export const extOrderId = validKey('extOrderId');
export const fee = validKey('fee');
export const feeCurrency = validKey('feeCurrency');
export const intOrderId = validKey('intOrderId');
export const price = validKey('price');
export const quantity = validKey('quantity');
export const updatedAt = validKey('updatedAt');
export const dateTime = validKey('dateTime');
export const instrument = validKey('instrument');
export const transactionType = validKey('__typename');
export const uuid = validKey('uuid');
export const portfolioName = validKey('portfolioName');
export const portfolioId = validKey('portfolioId');
export const feePortfolioId = validKey('feePortfolioId');
export const feePortfolioName = validKey('feePortfolioName');
export const feeAccountId = validKey('feeAccountId');
export const feeAccountName = validKey('feeAccountName');
export const baseCurrency = validKey('baseCurrency');
export const counterPortfolioName = validKey('counterPortfolioName');
export const counterPortfolioId = validKey('counterPortfolioId');
export const venueAccount = validKey('venueAccount');
export const venueAccountName = validKey('venueAccountName');
export const actions = 'actions';
export const rootExecution = validKey('rootExecution');
export const rootOrderId = validKey('rootOrderId');
export const settled = validKey('settled');
export const settledDateTime = validKey('settledDateTime');
export const settledTransactionIds = validKey('settledTransactionIds');
export const executionId = validKey('executionId');
export const sourceAccountId = validKey('sourceAccountId');
export const targetAccountId = validKey('targetAccountId');
export const targetAccountName = validKey('targetAccountName');
export const sourcePortfolioId = validKey('sourcePortfolioId');
export const targetPortfolioId = validKey('targetPortfolioId');
export const sourcePortfolioName = validKey('sourcePortfolioName');
export const targetPortfolioName = validKey('targetPortfolioName');
export const venueExecutionId = validKey('venueExecutionId');
export const parentOrderId = validKey('parentOrderId');
export const underlyingExecutionId = validKey('underlyingExecutionId');

export const transactionsHistoryDefinitions: ColDef<Transaction>[] = [
  getGenericField(orderId, {
    headerName: i18n.t('grid.columnHeader.orderId') as string,
    valueGetter: (params) => params.data?.orderId || params.data?.feeOrderId || '',
  }),
  getGenericField(currency),
  getGenericField(description),
  getGenericField(extOrderId, {
    headerName: i18n.t('grid.columnHeader.extOrderId') as string,
  }),
  getCurrencyBasedMoneyField(fee, feeCurrency, { noRounding: true }),
  getGenericField(feeCurrency),
  getGenericField(intOrderId, { headerName: i18n.t('grid.columnHeader.intOrderId') as string }),
  getCurrencyBasedMoneyField(price, currency),
  getGenericNumberField(quantity),
  getGenericField(updatedAt, {
    cellRenderer: DateRendererForMsTimestamp('updatedAt'),
    headerName: getDateColumnName('updatedAt'),
  }),
  getGenericField(dateTime, {
    cellRenderer: DateRendererToGoWithValueGetter(),
    valueGetter: ISODateValueGetter('dateTime'),
    headerName: getDateColumnName('dateTime'),
    sortable: true,
    sort: 'desc',
  }),
  getGenericField(uuid, { headerName: i18n.t('grid.columnHeader.transactionId') as string }),
  getGenericField(portfolioName),
  getGenericField(portfolioId),
  getGenericField(baseCurrency),
  getGenericField(counterPortfolioName),
  getGenericField(counterPortfolioId),
  getGenericField(feePortfolioId),
  getGenericField(feePortfolioName),
  getGenericField(feeAccountId),
  getGenericField(feeAccountName),
  getGenericField(executionId),
  getGenericField(sourceAccountId),
  getGenericField(targetAccountId),
  getGenericField(targetAccountName),
  getGenericField(sourcePortfolioId),
  getGenericField(targetPortfolioId),
  getGenericField(sourcePortfolioName),
  getGenericField(targetPortfolioName),
  getGenericField(venueExecutionId),
  getGenericField(parentOrderId),
  getGenericField(underlyingExecutionId),
  getGenericField(transactionType, {
    headerName: i18n.t('common.type') as string,
    valueGetter: (params) => formatTransactionTypeByTypename(i18n.t, params.data?.__typename),
  }),
  getGenericField(venueAccount),
  getGenericField(venueAccountName),
  getGenericField(rootExecution, {
    valueGetter: (params) => params.data?.rootExecution?.executionId,
    headerName: i18n.t('transactions.rootExecutionId') as string,
  }),
  getGenericField(rootOrderId, {
    valueGetter: (params) => params.data?.rootExecution?.orderId || params.data?.rootOrderId || '',
  }),
  getGenericField(settled),
  getGenericField(settledTransactionIds),
  getGenericField(settledDateTime, {
    cellRenderer: DateRendererForMsTimestamp('settledDateTime'),
    headerName: getDateColumnName('settledDateTime'),
  }),
  getGenericField(actions, {
    headerName: '',
    width: 40,
    minWidth: 40,
    maxWidth: 40,
    cellRenderer: ActionsRenderer,
    pinned: 'right',
    lockPinned: true,
  }),
];

export const defaultTransactionsHistoryVisibleFields = [
  orderId,
  uuid,
  baseCurrency,
  instrument,
  quantity,
  currency,
  price,
  fee,
  portfolioName,
  counterPortfolioName,
  venueAccountName,
  transactionType,
  settled,
  dateTime,
  actions,
];

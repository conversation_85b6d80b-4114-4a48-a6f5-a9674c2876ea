import { Paragraph } from '@ui/Typography/Paragraph';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { DATE_FORMATS } from '@wyden/date';
import { FiltersChipList } from '@wyden/features/chip-filters/FiltersChipList';
import { DatePicker } from '@wyden/features/chip-filters/filters/DatePicker';
import { TextArea } from '@wyden/features/chip-filters/filters/TextArea';
import { MultiselectList } from '@wyden/features/chip-filters/filters/multiselect/MultiselectList';
import {
  TransactionHistoryFilterLists,
  useTransactionHistoryFilters,
} from '@wyden/features/transactions-history/useTransactionHistoryFilters';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import {
  PortfolioResponse,
  useTransactionTypesQuery,
} from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import format from 'date-fns/format';
import { useTranslation } from 'react-i18next';
import { TransactionHistoryAccountsList } from '../chip-filters/AccountsListFilter';
import { TransactionHistoryPortfoliosList } from '../chip-filters/PortfoliosListFilter';
import { TransactionHistoryWalletsList } from '../chip-filters/WalletsListFilter';
import { useCurrencies } from '../currencies/useCurrencies';
import { TransactionType, useEnumFormatters } from '@wyden/hooks/useEnumFormatters';
import { formatChipLabels } from '@wyden/helpers/stringsHelpers';

const ColumnList = () => {
  const { setSelectedList } = useTransactionHistoryFilters();
  const { t } = useTranslation();

  return (
    <>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.ACCOUNTS_LIST);
        }}
      >
        {t('filters.accounts')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.WALLETS_LIST);
        }}
      >
        {t('filters.wallets')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.PORTFOLIOS_LIST);
        }}
      >
        {t('filters.portfolios')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.TRANSACTION_TYPES_LIST);
        }}
      >
        {t('transactionsHistory.transactionTypes')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.CREATED_FROM_INPUT);
        }}
      >
        {t('transactionsHistory.createdFrom')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.CREATED_TO_INPUT);
        }}
      >
        {t('transactionsHistory.createdTo')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.ORDER_ID_INPUT);
        }}
      >
        {t('transactionsHistory.orderId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.ROOT_EXECUTION_ID_INPUT);
        }}
      >
        {t('transactionsHistory.rootExecutionId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.EXECUTION_ID_INPUT);
        }}
      >
        {t('transactionsHistory.executionId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.VENUE_EXECUTION_ID_INPUT);
        }}
      >
        {t('transactionsHistory.venueExecutionId')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.CURRENCY_INPUT);
        }}
      >
        {t('transactionsHistory.currency')}
      </CategoryListParagraph>
      <CategoryListParagraph
        onClick={() => {
          setSelectedList(TransactionHistoryFilterLists.TRANSACTION_ID_INPUT);
        }}
      >
        {t('grid.columnHeader.transactionId')}
      </CategoryListParagraph>
    </>
  );
};

const CategoryListParagraph = styled(Paragraph)`
  padding: ${getSpacing(1)} ${getSpacing(2)};
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  cursor: pointer;
`;

interface ListProps {
  close: (e: { stopPropagation: () => void }) => void;
}

const CreatedFromInput = () => {
  const { t } = useTranslation();
  const { setCreatedFrom } = useTransactionHistoryFilters();
  const { filters } = useTransactionHistoryFilters();
  const selectedCreatedTo = filters.createdTo ?? undefined;
  const selectedCreatedFrom = filters.createdFrom ?? undefined;

  return (
    <DatePicker
      headerText={t('common.createdFrom')}
      onChange={(date) => {
        setCreatedFrom(date);
      }}
      maxDate={selectedCreatedTo}
      selectedDate={selectedCreatedFrom}
      label={t('common.createdFrom')}
    />
  );
};

const CreatedToInput = () => {
  const { t } = useTranslation();
  const { setCreatedTo } = useTransactionHistoryFilters();
  const { filters } = useTransactionHistoryFilters();
  const selectedCreatedTo = filters.createdTo ?? undefined;
  const selectedCreatedFrom = filters.createdFrom ?? undefined;

  return (
    <DatePicker
      headerText={t('common.createdTo')}
      onChange={(date) => {
        setCreatedTo(date);
      }}
      selectedDate={selectedCreatedTo}
      minDate={selectedCreatedFrom}
      label={t('common.createdTo')}
    />
  );
};

const OrderIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setOrderId,
    filters: { orderId },
  } = useTransactionHistoryFilters();
  return (
    <TextArea
      value={orderId || ''}
      headerText={t('transactionsHistory.orderId')}
      onApply={setOrderId}
      close={props.close}
    />
  );
};

const TransactionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setTransactionId,
    filters: { transactionId },
  } = useTransactionHistoryFilters();
  return (
    <TextArea
      value={transactionId || ''}
      headerText={t('grid.columnHeader.transactionId')}
      onApply={setTransactionId}
      close={props.close}
    />
  );
};

const RootExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setRootExecutionId,
    filters: { rootExecutionId },
  } = useTransactionHistoryFilters();
  return (
    <TextArea
      value={rootExecutionId || ''}
      headerText={t('transactionsHistory.rootExecutionId')}
      onApply={setRootExecutionId}
      close={props.close}
    />
  );
};

const ExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setExecutionId,
    filters: { executionId },
  } = useTransactionHistoryFilters();
  return (
    <TextArea
      value={executionId || ''}
      headerText={t('transactionsHistory.executionId')}
      onApply={setExecutionId}
      close={props.close}
    />
  );
};

const VenueExecutionIdInput = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    setVenueExecutionId,
    filters: { venueExecutionId },
  } = useTransactionHistoryFilters();
  return (
    <TextArea
      value={venueExecutionId || ''}
      headerText={t('transactionsHistory.venueExecutionId')}
      onApply={setVenueExecutionId}
      close={props.close}
    />
  );
};

const CurrencyInput = (props: ListProps) => {
  const { t } = useTranslation();

  const {
    setCurrency,
    filters: { currency },
  } = useTransactionHistoryFilters();

  const { loading, currencies } = useCurrencies();
  const currencyOptions = currencies.map((currency) => currency.symbol);

  return (
    <MultiselectList
      autocomplete
      headerText={t('transactionsHistory.currency')}
      options={currencyOptions}
      loading={loading}
      filters={currency}
      setFilters={(filters) => {
        setCurrency(filters);
      }}
      close={props.close}
    />
  );
};

const TransactionTypes = (props: ListProps) => {
  const { t } = useTranslation();
  const {
    toggleTransactionType,
    clearTransactionType,
    filters: { transactionType },
  } = useTransactionHistoryFilters();
  const { data } = useTransactionTypesQuery();
  const types = data?.transactionTypes || [];
  const { formatTransactionType } = useEnumFormatters();

  return (
    <MultiselectList
      headerText={t('transactionsHistory.transactionTypes')}
      options={types}
      filters={transactionType}
      close={props.close}
      toggleFilter={(filter) => {
        toggleTransactionType(filter);
      }}
      clearList={clearTransactionType}
      getTitle={(option) => formatTransactionType(option as TransactionType) || option}
    />
  );
};

const lists = {
  [TransactionHistoryFilterLists.COLUMN_LIST]: ColumnList,
  [TransactionHistoryFilterLists.ACCOUNTS_LIST]: TransactionHistoryAccountsList,
  [TransactionHistoryFilterLists.WALLETS_LIST]: TransactionHistoryWalletsList,
  [TransactionHistoryFilterLists.PORTFOLIOS_LIST]: TransactionHistoryPortfoliosList,
  [TransactionHistoryFilterLists.CREATED_FROM_INPUT]: CreatedFromInput,
  [TransactionHistoryFilterLists.CREATED_TO_INPUT]: CreatedToInput,
  [TransactionHistoryFilterLists.ORDER_ID_INPUT]: OrderIdInput,
  [TransactionHistoryFilterLists.ROOT_EXECUTION_ID_INPUT]: RootExecutionIdInput,
  [TransactionHistoryFilterLists.EXECUTION_ID_INPUT]: ExecutionIdInput,
  [TransactionHistoryFilterLists.VENUE_EXECUTION_ID_INPUT]: VenueExecutionIdInput,
  [TransactionHistoryFilterLists.CURRENCY_INPUT]: CurrencyInput,
  [TransactionHistoryFilterLists.TRANSACTION_TYPES_LIST]: TransactionTypes,
  [TransactionHistoryFilterLists.TRANSACTION_ID_INPUT]: TransactionIdInput,
};

export const TransactionsHistoryFilters = () => {
  const { t } = useTranslation();
  const { filters, clear, selectedList, setSelectedList, columnListSelected, onRemoveFilter } =
    useTransactionHistoryFilters((state) => {
      const filters = Object.entries(state.filters)
        .map(([key, value]) => {
          if (key === 'portfolios' && Array.isArray(value)) {
            return {
              key,
              values: value?.map((portfolio) => (portfolio as PortfolioResponse).name),
            };
          }
          if (key === 'accounts' && Array.isArray(value)) {
            return {
              key,
              values: value?.map((account) => (account as VenueAccountWithVenue).venueAccountName),
            };
          }
          if (key === 'wallets' && Array.isArray(value)) {
            return {
              key,
              values: value?.map((wallet) => (wallet as VenueAccountWithVenue).venueAccountName),
            };
          }
          if ((key === 'createdFrom' || key === 'createdTo') && value) {
            return {
              key,
              values: [format(new Date(value) as Date, DATE_FORMATS.DEFAULT_WITH_TIME)],
            };
          }
          if (key === 'transactionType' || key === 'currency') {
            return { key, values: value };
          }
          return { key, values: value ? [value] : [] };
        })
        .filter(({ values }) => values?.length > 0);
      return {
        ...state,
        filters,
      };
    });
  const List = lists[selectedList];
  const filtersKeyTranslation = {
    [TransactionHistoryFilterLists.TRANSACTION_TYPES_LIST]: t(
      'transactionsHistory.transactionTypes',
    ),
    [TransactionHistoryFilterLists.ACCOUNTS_LIST]: t('filters.accounts'),
    [TransactionHistoryFilterLists.WALLETS_LIST]: t('filters.wallets'),
    [TransactionHistoryFilterLists.PORTFOLIOS_LIST]: t('filters.portfolios'),
    [TransactionHistoryFilterLists.CREATED_FROM_INPUT]: t('transactionsHistory.createdFrom'),
    [TransactionHistoryFilterLists.CREATED_TO_INPUT]: t('transactionsHistory.createdTo'),
    [TransactionHistoryFilterLists.ORDER_ID_INPUT]: t('transactionsHistory.orderId'),
    [TransactionHistoryFilterLists.ROOT_EXECUTION_ID_INPUT]: t(
      'transactionsHistory.rootExecutionId',
    ),
    [TransactionHistoryFilterLists.EXECUTION_ID_INPUT]: t('transactionsHistory.executionId'),
    [TransactionHistoryFilterLists.VENUE_EXECUTION_ID_INPUT]: t(
      'transactionsHistory.venueExecutionId',
    ),
    [TransactionHistoryFilterLists.CURRENCY_INPUT]: t('transactionsHistory.currency'),
    [TransactionHistoryFilterLists.TRANSACTION_ID_INPUT]: t('grid.columnHeader.transactionId'),
  };
  const selectedFilters = filters.filter(({ key }) => key === selectedList);
  return (
    <Filters>
      <FiltersChipList
        clearSubFilters={() => {
          columnListSelected();
        }}
        filtersCount={filters.length}
        getGroupedFilters={(editMode) =>
          filters.filter(({ key }) => {
            if (selectedList === TransactionHistoryFilterLists.COLUMN_LIST || editMode) {
              return true;
            }
            return key !== selectedList;
          })
        }
        onEdit={(key) => {
          setSelectedList(key as TransactionHistoryFilterLists);
        }}
        onRemove={(key) => onRemoveFilter(key)}
        onReset={clear}
        keySelectionModeOn={selectedList === TransactionHistoryFilterLists.COLUMN_LIST}
        selectedKey={selectedList}
        selectedValues={selectedFilters.map(({ values }) => values).flat()}
        isKeyAdded={
          selectedList !== TransactionHistoryFilterLists.COLUMN_LIST &&
          filters.some(({ key }) => key === selectedList)
        }
        translateKey={(key) => {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          return key in filtersKeyTranslation ? filtersKeyTranslation[key] : key;
        }}
        transformDisplayChips={formatChipLabels}
      >
        {(close) => <List close={close} />}
      </FiltersChipList>
    </Filters>
  );
};

const Filters = styled('div')`
  display: flex;
  flex-direction: column;
  padding: ${getSpacing(1)};
`;

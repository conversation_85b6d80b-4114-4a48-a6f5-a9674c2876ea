import {
  ApiKeyStatus,
  DayOfTheWeek,
  LedgerEntryType,
  OrderCategory,
  OrderStatus,
  OrderType,
  PortfolioType,
  PreTradeCheckLevel,
  QuoteTtlUnit,
  Resource,
  Scope,
  VenueType,
} from '@wyden/services/graphql/generated/graphql';
import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';

const PortfolioTypeKeys = {
  [PortfolioType.Nostro]: 'common.nostro',
  [PortfolioType.Vostro]: 'common.vostro',
} as const;

const ResourceKeys = {
  [Resource.ApiKey]: 'common.resources.apiKey',
  [Resource.Portfolio]: 'common.resources.portfolio',
  [Resource.PortfolioVostro]: 'common.resources.vostroPortfolio',
  [Resource.PortfolioNostro]: 'common.resources.nostroPortfolio',
  [Resource.Settlement]: 'common.resources.settlement',
  [Resource.ClientInstrument]: 'common.resources.clientInstrument',
  [Resource.ClobInstrument]: 'common.resources.clobInstrument',
  [Resource.VenueAccount]: 'common.resources.venueAccount',
  [Resource.Wallet]: 'common.resources.wallet',
  [Resource.WalletVostro]: 'common.resources.vostroWallet',
  [Resource.WalletNostro]: 'common.resources.nostroWallet',
  [Resource.Risk]: 'common.resources.risk',
  [Resource.BrokerConfig]: 'common.resources.brokerConfig',
  [Resource.Currency]: 'common.resources.currency',
  [Resource.Venue]: 'common.resources.venue',
};

const OrderStatusKeys = {
  [OrderStatus.Calculated]: 'orderStatuses.calculated',
  [OrderStatus.Canceled]: 'orderStatuses.canceled',
  [OrderStatus.Expired]: 'orderStatuses.expired',
  [OrderStatus.Filled]: 'orderStatuses.filled',
  [OrderStatus.New]: 'orderStatuses.new',
  [OrderStatus.OrderStatusUnspecified]: 'orderStatuses.orderStatusUnspecified',
  [OrderStatus.PartiallyFilled]: 'orderStatuses.partiallyFilled',
  [OrderStatus.PendingCancel]: 'orderStatuses.pendingCancel',
  [OrderStatus.PendingNew]: 'orderStatuses.pendingNew',
  [OrderStatus.PendingReplace]: 'orderStatuses.pendingReplace',
  [OrderStatus.Rejected]: 'orderStatuses.rejected',
  [OrderStatus.Replaced]: 'orderStatuses.replaced',
};

const OrderTypeKeys = {
  [OrderType.Market]: 'common.orderTypes.market',
  [OrderType.Limit]: 'common.orderTypes.limit',
  [OrderType.Stop]: 'common.orderTypes.stop',
  [OrderType.StopLimit]: 'common.orderTypes.stopLimit',
  [OrderType.MarketCash]: 'common.orderTypes.marketCash',
  [OrderType.LimitCash]: 'common.orderTypes.limitCash',
  [OrderType.StopCash]: 'common.orderTypes.stopCash',
  [OrderType.StopLimitCash]: 'common.orderTypes.stopLimitCash',
  [OrderType.PreviouslyIndicated]: 'common.orderTypes.previouslyIndicated',
  [OrderType.OrderTypeUnspecified]: 'common.orderTypes.unspecified',
};

const OrderCategoryKeys = {
  [OrderCategory.AgencyClobOrder]: 'common.orderCategory.agencyClobOrder',
  [OrderCategory.AgencyOrder]: 'common.orderCategory.agencyOrder',
  [OrderCategory.AgencySorOrder]: 'common.orderCategory.agencySorOrder',
  [OrderCategory.AgencyStreetOrder]: 'common.orderCategory.agencyStreetOrder',
  [OrderCategory.AutoHedgerExternalHedgeOrder]: 'common.orderCategory.autoHedgerExternalHedgeOrder',
  [OrderCategory.ClobExternalHedgeOrder]: 'common.orderCategory.clobExternalHedgeOrder',
  [OrderCategory.ClobQuotingOrder]: 'common.orderCategory.clobQuotingOrder',
  [OrderCategory.DirectMarketAccessOrder]: 'common.orderCategory.directMarketAccessOrder',
  [OrderCategory.SorChildOrder]: 'common.orderCategory.sorChildOrder',
  [OrderCategory.SorOrder]: 'common.orderCategory.sorOrder',
  [OrderCategory.OrderCategoryUnspecified]: '',
};

const ScopesKeys = {
  [Scope.Manage]: 'common.scopes.manage',
  [Scope.Create]: 'common.scopes.create',
  [Scope.Read]: 'common.scopes.read',
  [Scope.Trade]: 'common.scopes.trade',
};

const DayOfTheWeekKeys = {
  [DayOfTheWeek.Monday]: 'common.dayOfTheWeek.monday',
  [DayOfTheWeek.Tuesday]: 'common.dayOfTheWeek.tuesday',
  [DayOfTheWeek.Wednesday]: 'common.dayOfTheWeek.wednesday',
  [DayOfTheWeek.Thursday]: 'common.dayOfTheWeek.thursday',
  [DayOfTheWeek.Friday]: 'common.dayOfTheWeek.friday',
  [DayOfTheWeek.Saturday]: 'common.dayOfTheWeek.saturday',
  [DayOfTheWeek.Sunday]: 'common.dayOfTheWeek.sunday',
};

const TransactionTypeTypenameKeys = {
  ClientAssetTrade: 'common.transactionTypes.clientAssetTrade',
  ClientCashTrade: 'common.transactionTypes.clientCashTrade',
  StreetAssetTrade: 'common.transactionTypes.streetAssetTrade',
  StreetCashTrade: 'common.transactionTypes.streetCashTrade',
  Withdrawal: 'common.transactionTypes.withdrawal',
  Deposit: 'common.transactionTypes.deposit',
  AccountCashTransfer: 'common.transactionTypes.accountCashTransfer',
  Fee: 'common.transactionTypes.fee',
  PortfolioCashTransfer: 'common.transactionTypes.portfolioCashTransfer',
  Settlement: 'common.transactionTypes.settlement',
};

// TODO https://algotrader.atlassian.net/browse/AC-6029 remove this when all types will be in the schema
export enum TransactionType {
  ClientAssetTrade = 'CLIENT_ASSET_TRADE',
  ClientCashTrade = 'CLIENT_CASH_TRADE',
  StreetAssetTrade = 'STREET_ASSET_TRADE',
  StreetCashTrade = 'STREET_CASH_TRADE',
  Withdrawal = 'WITHDRAWAL',
  Deposit = 'DEPOSIT',
  AccountCashTransfer = 'ACCOUNT_CASH_TRANSFER',
  PortfolioCashTransfer = 'PORTFOLIO_CASH_TRANSFER',
  Fee = 'FEE',
  Settlement = 'SETTLEMENT',
}

const TransactionTypeKeys = {
  [TransactionType.ClientAssetTrade]: 'common.transactionTypes.clientAssetTrade',
  [TransactionType.ClientCashTrade]: 'common.transactionTypes.clientCashTrade',
  [TransactionType.StreetAssetTrade]: 'common.transactionTypes.streetAssetTrade',
  [TransactionType.StreetCashTrade]: 'common.transactionTypes.streetCashTrade',
  [TransactionType.Withdrawal]: 'common.transactionTypes.withdrawal',
  [TransactionType.Deposit]: 'common.transactionTypes.deposit',
  [TransactionType.AccountCashTransfer]: 'common.transactionTypes.accountCashTransfer',
  [TransactionType.PortfolioCashTransfer]: 'common.transactionTypes.portfolioCashTransfer',
  [TransactionType.Settlement]: 'common.transactionTypes.settlement',
  [TransactionType.Fee]: 'common.transactionTypes.fee',
};

const LedgerTypeKeys = {
  [LedgerEntryType.AssetTradeBuy]: 'common.ledgerTypes.assetTradeBuy',
  [LedgerEntryType.AssetTradeSell]: 'common.ledgerTypes.assetTradeSell',
  [LedgerEntryType.CashTradeCredit]: 'common.ledgerTypes.cashTradeCredit',
  [LedgerEntryType.CashTradeDebit]: 'common.ledgerTypes.cashTradeDebit',
  [LedgerEntryType.AssetTradeProceeds]: 'common.ledgerTypes.assetTradeProceeds',
  [LedgerEntryType.Deposit]: 'common.ledgerTypes.deposit',
  [LedgerEntryType.Withdrawal]: 'common.ledgerTypes.withdrawal',
  [LedgerEntryType.Transfer]: 'common.ledgerTypes.transfer',
  [LedgerEntryType.Fee]: 'common.ledgerTypes.fee',
  [LedgerEntryType.TradingFee]: 'common.ledgerTypes.tradingFee',
  [LedgerEntryType.DepositFee]: 'common.ledgerTypes.depositFee',
  [LedgerEntryType.WithdrawalFee]: 'common.ledgerTypes.withdrawalFee',
  [LedgerEntryType.TransferFee]: 'common.ledgerTypes.transferFee',
  [LedgerEntryType.Reservation]: 'common.ledgerTypes.reservation',
  [LedgerEntryType.ReservationRelease]: 'common.ledgerTypes.reservationRelease',
  [LedgerEntryType.ReservationReleaseRemaining]: 'common.ledgerTypes.reservationReleaseRemaining',
  [LedgerEntryType.WithdrawalReservation]: 'common.ledgerTypes.withdrawalReservation',
};

const ApiKeyStatusKeys = {
  [ApiKeyStatus.Active]: 'common.apiKeyStatus.active',
  [ApiKeyStatus.Inactive]: 'common.apiKeyStatus.inactive',
};

const VenueTypeKeys = {
  [VenueType.Street]: 'common.venueType.street',
  [VenueType.Client]: 'common.venueType.client',
  [VenueType.Clob]: 'common.venueType.clob',
};

const UnitsKeys = {
  [QuoteTtlUnit.Days]: 'common.units.days',
  [QuoteTtlUnit.Hours]: 'common.units.hours',
  [QuoteTtlUnit.Minutes]: 'common.units.minutes',
  [QuoteTtlUnit.Seconds]: 'common.units.seconds',
  [QuoteTtlUnit.Milliseconds]: 'common.units.milliseconds',
};

const CheckLEvel = {
  [PreTradeCheckLevel.Block]: 'common.preTradeCheckLevel.block',
  [PreTradeCheckLevel.Warn]: 'common.preTradeCheckLevel.warn',
};

const LastRequestResultKeys = {
  SUCCESS: 'common.lastRequestResult.success',
  SELF_MATCH: 'common.lastRequestResult.selfMatch',
  EXTERNAL_VENUE_REJECT: 'common.lastRequestResult.externalVenueReject',
  CONNECTOR_UNAVAILABLE: 'common.lastRequestResult.connectorUnavailable',
  ACCESS_DENIED_INSUFFICIENT_RIGHTS: 'common.lastRequestResult.accessDeniedInsufficientRights',
  OEMS_ORDER_REGISTRATION: 'common.lastRequestResult.oemsOrderRegistration',
};

function getTranslation<T, Z extends keyof T>(
  t: TFunction<'translation', undefined>,
  map: T,
  key: Z | undefined | null,
) {
  const translationString = key && (map[key] as string | undefined);
  return translationString ? t(translationString) : key || '';
}

export function formatPortfolioType(
  t: TFunction<'translation', undefined>,
  type: PortfolioType | undefined | null,
) {
  return getTranslation(t, PortfolioTypeKeys, type);
}

export function formatResource(
  t: TFunction<'translation', undefined>,
  resource: Resource | undefined | null,
) {
  return getTranslation(t, ResourceKeys, resource);
}

export function formatDayOfTheWeek(
  t: TFunction<'translation', undefined>,
  day: DayOfTheWeek | undefined | null,
) {
  return day ? t(DayOfTheWeekKeys[day]) : day;
}

export function formatOrderStatus(
  t: TFunction<'translation', undefined>,
  status: OrderStatus | undefined | null,
) {
  return getTranslation(t, OrderStatusKeys, status);
}

export function formatOrderType(
  t: TFunction<'translation', undefined>,
  type: OrderType | undefined | null,
) {
  return getTranslation(t, OrderTypeKeys, type);
}

export function formatOrderCategory(
  t: TFunction<'translation', undefined>,
  type: keyof typeof OrderCategoryKeys | undefined | null,
) {
  return getTranslation(t, OrderCategoryKeys, type);
}

export function formatScope(
  t: TFunction<'translation', undefined>,
  scope: Scope | undefined | null,
) {
  return getTranslation(t, ScopesKeys, scope);
}

export function formatTransactionTypeByTypename(
  t: TFunction<'translation', undefined>,
  type: keyof typeof TransactionTypeTypenameKeys | undefined | null,
) {
  return getTranslation(t, TransactionTypeTypenameKeys, type);
}

export function formatTransactionType(
  t: TFunction<'translation', undefined>,
  type: keyof typeof TransactionTypeKeys | undefined | null,
) {
  return getTranslation(t, TransactionTypeKeys, type);
}

export function formatLedgerType(
  t: TFunction<'translation', undefined>,
  type: keyof typeof LedgerTypeKeys | undefined | null,
) {
  return getTranslation(t, LedgerTypeKeys, type);
}

export function formatApiKeyStatus(
  t: TFunction<'translation', undefined>,
  type: keyof typeof ApiKeyStatusKeys | undefined | null,
) {
  return getTranslation(t, ApiKeyStatusKeys, type);
}

export function formatVenueType(
  t: TFunction<'translation', undefined>,
  type: keyof typeof VenueTypeKeys | undefined | null,
) {
  return getTranslation(t, VenueTypeKeys, type);
}

export function formatLastRequestResult(
  t: TFunction<'translation', undefined>,
  type: keyof typeof LastRequestResultKeys | undefined | null,
) {
  return getTranslation(t, LastRequestResultKeys, type);
}

export function formatUnits(
  t: TFunction<'translation', undefined>,
  unit: QuoteTtlUnit | undefined | null,
) {
  return unit ? t(UnitsKeys[unit]) : unit;
}

export function formatPreTradeCheckLevel(
  t: TFunction<'translation', undefined>,
  level: PreTradeCheckLevel | undefined | null,
) {
  return level ? t(CheckLEvel[level]) : level;
}

export function useEnumFormatters() {
  const { t } = useTranslation();

  const formatPortfolioType = (type?: PortfolioType | null) => {
    return getTranslation(t, PortfolioTypeKeys, type);
  };

  const formatOrderStatus = (status: OrderStatus | undefined | null) => {
    return getTranslation(t, OrderStatusKeys, status);
  };

  const formatOrderType = (type: OrderType | undefined | null) => {
    return getTranslation(t, OrderTypeKeys, type);
  };

  const formatOrderCategory = (type: OrderCategory | undefined | null) => {
    return getTranslation(t, OrderCategoryKeys, type);
  };

  const formatScope = (scope: Scope | undefined | null) => {
    return getTranslation(t, ScopesKeys, scope);
  };

  const formatResource = (resource: Resource | undefined | null) => {
    return getTranslation(t, ResourceKeys, resource);
  };

  const formatTransactionTypeByTypename = (
    type: keyof typeof TransactionTypeTypenameKeys | undefined | null,
  ) => {
    return getTranslation(t, TransactionTypeTypenameKeys, type);
  };

  const formatTransactionType = (type: keyof typeof TransactionTypeKeys | undefined | null) => {
    return getTranslation(t, TransactionTypeKeys, type);
  };

  const formatLedgerType = (type: keyof typeof LedgerTypeKeys | undefined | null) => {
    return getTranslation(t, LedgerTypeKeys, type);
  };

  const formatDayOfTheWeek = (day: DayOfTheWeek | undefined | null) => {
    return day ? t(DayOfTheWeekKeys[day]) : day;
  };

  const formatUnits = (unit: QuoteTtlUnit | undefined | null) => {
    return unit ? t(UnitsKeys[unit]) : unit;
  };

  const formatApiKeyStatus = (type: keyof typeof ApiKeyStatusKeys | undefined | null) => {
    return type && ApiKeyStatusKeys[type] ? t(ApiKeyStatusKeys[type]) : type;
  };

  const formatCheckLevel = (level: PreTradeCheckLevel | undefined | null) => {
    return level ? t(CheckLEvel[level]) : level;
  };

  return {
    formatPortfolioType,
    formatOrderStatus,
    formatOrderType,
    formatOrderCategory,
    formatScope,
    formatResource,
    formatTransactionType,
    formatTransactionTypeByTypename,
    formatLedgerType,
    formatDayOfTheWeek,
    formatUnits,
    formatApiKeyStatus,
    formatCheckLevel,
  };
}

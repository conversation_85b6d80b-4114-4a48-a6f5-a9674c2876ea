package io.wyden.connector.referencedata.io;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.referencedata.StreetSideInstrumentListSnapshot;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InstrumentsResponseEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(InstrumentsResponseEmitter.class);

    private final RabbitExchange<StreetSideInstrumentListSnapshot> instrumentsResponseExchange;

    public InstrumentsResponseEmitter(RabbitIntegrator rabbitIntegrator) {
        this.instrumentsResponseExchange = OemsExchange.ReferenceData.declareReferenceDataInstrumentsResponse(rabbitIntegrator);
    }

    public void emit(StreetSideInstrumentListSnapshot instrumentListSnapshot) {
        try {
            LOGGER.info("Emitting {} street-side instruments to {}", instrumentListSnapshot.getInstrumentList().size(), instrumentsResponseExchange.getName());
            instrumentsResponseExchange.publish(instrumentListSnapshot, StringUtils.EMPTY);
        } catch (Exception ex) {
            LOGGER.error("Exception in ReferenceDataResponseEmitter", ex);
        }
    }
}

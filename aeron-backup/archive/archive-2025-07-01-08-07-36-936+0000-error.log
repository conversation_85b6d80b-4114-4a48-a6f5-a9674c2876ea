
1 observations from 2025-06-30 10:09:41.973+0000 to 2025-06-30 10:09:41.973+0000 for:
io.aeron.exceptions.DriverTimeoutException: FATAL - MediaDriver (/dev/shm/aeron-aeron) keepalive: age=29362ms > timeout=10000ms
	at io.aeron.ClientConductor.checkLiveness(ClientConductor.java:1601)
	at io.aeron.ClientConductor.checkTimeouts(ClientConductor.java:1564)
	at io.aeron.ClientConductor.service(ClientConductor.java:1470)
	at io.aeron.ClientConductor.doWork(ClientConductor.java:196)
	at org.agrona.concurrent.AgentInvoker.invoke(AgentInvoker.java:147)
	at io.aeron.archive.ArchiveConductor.invokeAeronInvoker(ArchiveConductor.java:332)
	at io.aeron.archive.ArchiveConductor.doWork(ArchiveConductor.java:301)
	at io.aeron.archive.DedicatedModeArchiveConductor.doWork(DedicatedModeArchiveConductor.java:59)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-30 10:09:41.973+0000 to 2025-06-30 10:09:41.973+0000 for:
org.agrona.concurrent.AgentTerminationException
	at io.aeron.ClientConductor.doWork(ClientConductor.java:193)
	at org.agrona.concurrent.AgentInvoker.invoke(AgentInvoker.java:147)
	at io.aeron.archive.ArchiveConductor.invokeAeronInvoker(ArchiveConductor.java:332)
	at io.aeron.archive.ArchiveConductor.doWork(ArchiveConductor.java:301)
	at io.aeron.archive.DedicatedModeArchiveConductor.doWork(DedicatedModeArchiveConductor.java:59)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


2 distinct errors observed.

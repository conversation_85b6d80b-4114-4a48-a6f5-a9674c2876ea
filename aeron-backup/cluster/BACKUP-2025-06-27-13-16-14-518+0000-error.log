
161 observations from 2025-06-27 08:37:35.516+0000 to 2025-06-27 10:25:41.301+0000 for:
io.aeron.exceptions.TimeoutException: ERROR - Archive connect timeout: step=AWAIT_PUBLICATION_CONNECTED publication.uri=aeron:udp?term-length=65536|sparse=true|mtu=1408|endpoint=0.0.0.0:9001
	at io.aeron.archive.client.AeronArchive$AsyncConnect.checkDeadline(AeronArchive.java:3863)
	at io.aeron.archive.client.AeronArchive$AsyncConnect.poll(AeronArchive.java:3705)
	at io.aeron.cluster.ClusterBackupAgent.snapshotRetrieve(ClusterBackupAgent.java:716)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:268)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-27 10:26:40.422+0000 to 2025-06-27 10:26:40.422+0000 for:
io.aeron.exceptions.DriverTimeoutException: FATAL - no response from MediaDriver within 10000000000ns
	at io.aeron.ClientConductor.awaitResponse(ClientConductor.java:1552)
	at io.aeron.ClientConductor.addExclusivePublication(ClientConductor.java:473)
	at io.aeron.Aeron.addExclusivePublication(Aeron.java:294)
	at io.aeron.cluster.PublicationGroup.next(PublicationGroup.java:63)
	at io.aeron.cluster.ClusterBackupAgent.backupQuery(ClusterBackupAgent.java:681)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:264)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-27 10:26:40.658+0000 to 2025-06-27 10:26:40.658+0000 for:
io.aeron.exceptions.ConductorServiceTimeoutException: FATAL - service interval exceeded: timeout=10000000000ns, interval=19118455509ns
	at io.aeron.ClientConductor.checkServiceInterval(ClientConductor.java:1577)
	at io.aeron.ClientConductor.checkTimeouts(ClientConductor.java:1561)
	at io.aeron.ClientConductor.service(ClientConductor.java:1470)
	at io.aeron.ClientConductor.doWork(ClientConductor.java:196)
	at org.agrona.concurrent.AgentInvoker.invoke(AgentInvoker.java:147)
	at io.aeron.cluster.ClusterBackupAgent.slowTick(ClusterBackupAgent.java:624)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:256)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-27 10:26:40.662+0000 to 2025-06-27 10:26:40.662+0000 for:
io.aeron.cluster.client.ClusterException: WARN - local archive not connected
	at io.aeron.cluster.ClusterBackupAgent.pollBackupArchiveEvents(ClusterBackupAgent.java:1071)
	at io.aeron.cluster.ClusterBackupAgent.slowTick(ClusterBackupAgent.java:636)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:256)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-27 10:26:40.673+0000 to 2025-06-27 10:26:40.673+0000 for:
org.agrona.concurrent.AgentTerminationException
	at io.aeron.cluster.ClusterBackupAgent.pollBackupArchiveEvents(ClusterBackupAgent.java:1072)
	at io.aeron.cluster.ClusterBackupAgent.slowTick(ClusterBackupAgent.java:636)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:256)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


5 distinct errors observed.

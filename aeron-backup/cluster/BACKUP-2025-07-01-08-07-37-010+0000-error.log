
50 observations from 2025-06-30 09:36:08.036+0000 to 2025-06-30 10:08:49.643+0000 for:
io.aeron.exceptions.TimeoutException: ERROR - Archive connect timeout: step=AWAIT_PUBLICATION_CONNECTED publication.uri=aeron:udp?term-length=65536|sparse=true|mtu=1408|endpoint=0.0.0.0:9001
	at io.aeron.archive.client.AeronArchive$AsyncConnect.checkDeadline(AeronArchive.java:3863)
	at io.aeron.archive.client.AeronArchive$AsyncConnect.poll(AeronArchive.java:3705)
	at io.aeron.cluster.ClusterBackupAgent.snapshotRetrieve(ClusterBackupAgent.java:716)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:268)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-30 10:09:42.109+0000 to 2025-06-30 10:09:42.109+0000 for:
io.aeron.exceptions.ClientTimeoutException: FATAL - client timeout from driver
	at io.aeron.ClientConductor.onClientTimeout(ClientConductor.java:426)
	at io.aeron.DriverEventsAdapter.onMessage(DriverEventsAdapter.java:248)
	at org.agrona.concurrent.broadcast.CopyBroadcastReceiver.receive(CopyBroadcastReceiver.java:116)
	at io.aeron.DriverEventsAdapter.receive(DriverEventsAdapter.java:69)
	at io.aeron.ClientConductor.service(ClientConductor.java:1471)
	at io.aeron.ClientConductor.doWork(ClientConductor.java:196)
	at org.agrona.concurrent.AgentInvoker.invoke(AgentInvoker.java:147)
	at io.aeron.cluster.ClusterBackupAgent.slowTick(ClusterBackupAgent.java:624)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:256)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-30 10:09:42.110+0000 to 2025-06-30 10:09:42.110+0000 for:
io.aeron.cluster.client.ClusterException: WARN - local archive not connected
	at io.aeron.cluster.ClusterBackupAgent.pollBackupArchiveEvents(ClusterBackupAgent.java:1071)
	at io.aeron.cluster.ClusterBackupAgent.slowTick(ClusterBackupAgent.java:636)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:256)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


1 observations from 2025-06-30 10:09:42.110+0000 to 2025-06-30 10:09:42.110+0000 for:
org.agrona.concurrent.AgentTerminationException
	at io.aeron.cluster.ClusterBackupAgent.pollBackupArchiveEvents(ClusterBackupAgent.java:1072)
	at io.aeron.cluster.ClusterBackupAgent.slowTick(ClusterBackupAgent.java:636)
	at io.aeron.cluster.ClusterBackupAgent.doWork(ClusterBackupAgent.java:256)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


4 distinct errors observed.

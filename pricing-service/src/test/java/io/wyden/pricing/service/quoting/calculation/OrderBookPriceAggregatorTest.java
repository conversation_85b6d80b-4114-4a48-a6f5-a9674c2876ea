package io.wyden.pricing.service.quoting.calculation;

import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.OrderBookLevel;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class OrderBookPriceAggregatorTest {

    @Test
    void testAggregation() {
        // Set up input order book
        OrderBook inputOrderBook = OrderBook.newBuilder()
            .putAsks("10.2", OrderBookLevel.newBuilder().setPrice("10.2").setAmount("5.0").build())
            .putAsks("10.5", OrderBookLevel.newBuilder().setPrice("10.5").setAmount("3.0").build())
            .putAsks("10.6", OrderBookLevel.newBuilder().setPrice("10.6").setAmount("3.0").build())
            .putAsks("10.8", OrderBookLevel.newBuilder().setPrice("10.8").setAmount("2.0").build())
            .putAsks("11.1", OrderBookLevel.newBuilder().setPrice("11.1").setAmount("1.0").build())

            .putBids("10.1", OrderBookLevel.newBuilder().setPrice("10.1").setAmount("3.0").build())
            .putBids("9.8", OrderBookLevel.newBuilder().setPrice("9.8").setAmount("7.0").build())
            .putBids("9.5", OrderBookLevel.newBuilder().setPrice("9.5").setAmount("6.0").build())
            .putBids("9.2", OrderBookLevel.newBuilder().setPrice("9.2").setAmount("4.0").build())
            .putBids("9.1", OrderBookLevel.newBuilder().setPrice("9.1").setAmount("1.0").build())
            .build();

        MarketDataEvent.Builder mde = MarketDataEvent.newBuilder()
            .setOrderBook(inputOrderBook);

        // GO!
        MarketDataEvent.Builder outputMde = OrderBookPriceAggregator.aggregate(mde, new BigDecimal("0.5"));
        OrderBook result = outputMde.getOrderBook();

        // Expected results
        Map<String, String> expectedAsks = new HashMap<>();
        expectedAsks.put("10.0", "5.0");
        expectedAsks.put("10.5", "6.0");
        expectedAsks.put("11.0", "3.0");

        Map<String, String> expectedBids = new HashMap<>();
        expectedBids.put("9.0", "5.0");
        expectedBids.put("9.5", "6.0");
        expectedBids.put("10.0", "10.0");

        // Verify asks
        for (Map.Entry<String, String> expected : expectedAsks.entrySet()) {
            assertTrue(result.getAsksMap().containsKey(expected.getKey()));
            assertEquals(expected.getValue(), result.getAsksMap().get(expected.getKey()).getAmount());
        }

        // Verify bids
        for (Map.Entry<String, String> expected : expectedBids.entrySet()) {
            assertTrue(result.getBidsMap().containsKey(expected.getKey()));
            assertEquals(expected.getValue(), result.getBidsMap().get(expected.getKey()).getAmount());
        }
    }
}
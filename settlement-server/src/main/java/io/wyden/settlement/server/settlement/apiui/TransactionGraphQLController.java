package io.wyden.settlement.server.settlement.apiui;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.settlement.client.transaction.SettlementStreetCashTrade;
import io.wyden.settlement.server.settlement.MutationSubmittedResponse;
import io.wyden.settlement.server.settlement.run.SettlementRunSinkWrapper;
import io.wyden.settlement.client.transaction.SelectAllTransactionInput;
import io.wyden.settlement.client.transaction.SelectTransactionsInput;
import io.wyden.settlement.client.transaction.TransactionSearchInput;
import io.wyden.settlement.server.settlement.transaction.TransactionService;
import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.graphql.data.method.annotation.QueryMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

@Controller
public class TransactionGraphQLController {
    private final TransactionService transactionService;
    private final SettlementRunSinkWrapper settlementRunSinkWrapper;

    public TransactionGraphQLController(TransactionService transactionService, SettlementRunSinkWrapper settlementRunSinkWrapper) {
        this.transactionService = transactionService;
        this.settlementRunSinkWrapper = settlementRunSinkWrapper;
    }

    @QueryMapping
    @PreAuthorize("hasPermission('settlement', 'read')")
    public Mono<PaginationModel.CursorConnection<SettlementStreetCashTrade>> settlementTransactions(@Argument(
        "search") TransactionSearchInput searchInput) {
        return Mono.just(transactionService.queryTransactions(searchInput));
    }

    @MutationMapping
    @PreAuthorize("hasPermission('settlement', 'manage')")
    public Mono<MutationSubmittedResponse> settlementTransactionsAllSelect(@Argument SelectAllTransactionInput request) {
        long runId = transactionService.selectAllTransactions(request);
        settlementRunSinkWrapper.fetchAndEmitCurrentStateByRunId(runId);
        return Mono.just(new MutationSubmittedResponse("Transactions all selected"));
    }

    @MutationMapping
    @PreAuthorize("hasPermission('settlement', 'manage')")
    public Mono<MutationSubmittedResponse> settlementTransactionsSelect(@Argument SelectTransactionsInput request) {
        transactionService.selectTransactions(request.settlementRunId(), request.changes());
        settlementRunSinkWrapper.fetchAndEmitCurrentStateByRunId(Long.parseLong(request.settlementRunId()));
        return Mono.just(new MutationSubmittedResponse("Transactions all selected"));
    }


}

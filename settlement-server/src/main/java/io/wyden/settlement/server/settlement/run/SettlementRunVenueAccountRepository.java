package io.wyden.settlement.server.settlement.run;

import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class SettlementRunVenueAccountRepository {

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public SettlementRunVenueAccountRepository(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public void saveAll(Long settlementRunId, List<String> venueAccountIds) {
        if (venueAccountIds == null || venueAccountIds.isEmpty()) {
            return;
        }

        for (String venueAccountId : venueAccountIds) {
            MapSqlParameterSource params = new MapSqlParameterSource()
                .addValue("settlementRunId", settlementRunId)
                .addValue("venueAccountId", venueAccountId);

            KeyHolder keyHolder = new GeneratedKeyHolder();
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM settlement_run_venue_account " +
                "WHERE settlement_run_id = :settlementRunId AND venue_account_id = :venueAccountId",
                params,
                Integer.class
            );

            if (count != null && count == 0) {
                jdbcTemplate.update(
                    "INSERT INTO settlement_run_venue_account (settlement_run_id, venue_account_id) " +
                    "VALUES (:settlementRunId, :venueAccountId)",
                    params,
                    keyHolder
                );
            }
        }
    }

    public List<String> findVenueAccountIdsBySettlementRunId(Long settlementRunId) {
        MapSqlParameterSource params = new MapSqlParameterSource()
            .addValue("settlementRunId", settlementRunId);

        return jdbcTemplate.query(
            "SELECT venue_account_id FROM settlement_run_venue_account WHERE settlement_run_id = :settlementRunId",
            params,
            (rs, rowNum) -> rs.getString("venue_account_id")
        );
    }

    public void deleteBySettlementRunId(Long settlementRunId) {
        MapSqlParameterSource params = new MapSqlParameterSource()
            .addValue("settlementRunId", settlementRunId);

        jdbcTemplate.update(
            "DELETE FROM settlement_run_venue_account WHERE settlement_run_id = :settlementRunId",
            params
        );
    }
}
package io.wyden.settlement.server.settlement.run;

import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegRepository;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;

@Repository
@Component
public class SettlementRunRepository {
    private final JdbcTemplate jdbcTemplate;
    private final SettlementLegRepository settlementLegRepository;
    private final SettlementRunVenueAccountRepository venueAccountRepository;

    public SettlementRunRepository(JdbcTemplate jdbcTemplate, SettlementLegRepository settlementLegRepository, SettlementRunVenueAccountRepository venueAccountRepository) {
        this.jdbcTemplate = jdbcTemplate;
        this.settlementLegRepository = settlementLegRepository;
        this.venueAccountRepository = venueAccountRepository;
    }

    public long persist(SettlementRunEntity entity) {
        final long runId;

        if (entity.id() == null) {
            runId = storeEmptySettlementRun(entity);
        } else {
            runId = entity.id();
            updateSettlementRun(entity);
        }

        if (entity.venueAccountIds() != null && !entity.venueAccountIds().isEmpty()) {
            venueAccountRepository.saveAll(runId, entity.venueAccountIds());
        }

        entity.legs()
            .forEach(leg -> {
                SettlementLegEntity legEntity = leg.toBuilder().runId(runId).build();
                settlementLegRepository.persist(legEntity);
            });

        return runId;
    }

    private void updateSettlementRun(SettlementRunEntity settlementRunEntity) {
        String sql = "UPDATE settlement_run SET created_at = ?, start_date = ?, status = ? WHERE id = ?";

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setTimestamp(1, settlementRunEntity.createdAt());
            ps.setDate(2, settlementRunEntity.startAt());
            ps.setString(3, settlementRunEntity.status().name());
            ps.setLong(4, settlementRunEntity.id());
            return ps;
        });
    }

    private long storeEmptySettlementRun(SettlementRunEntity settlementRunEntity) {
        String sql = "INSERT INTO settlement_run (created_at, start_date, status) VALUES (?, ?, ?)";
        KeyHolder keyHolder = new GeneratedKeyHolder();

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
            ps.setTimestamp(1, settlementRunEntity.createdAt());
            ps.setDate(2, settlementRunEntity.startAt());
            ps.setString(3, settlementRunEntity.status().name());
            return ps;
        }, keyHolder);
        return keyHolder.getKey().longValue();
    }

    public List<SettlementRunEntity> findAll() {
        String sql = "SELECT * FROM settlement_run";
        return jdbcTemplate.query(sql, (rs, rowNum) -> map(rs));
    }

    public List<SettlementRunEntity> findAllRunsInFinalState(LocalDate startDate) {
        String sql = "SELECT * FROM settlement_run WHERE CAST(start_date AS DATE) = ? AND status IN ('COMPLETED', 'CANCELED') ";
        return jdbcTemplate.query(sql, (rs, rowNum) -> map(rs), startDate);
    }

    public List<SettlementRunEntity> findAllRunsInNonFinalState() {
        String sql = "SELECT * FROM settlement_run WHERE status IN ('IN_PROGRESS', 'PENDING') ";
        return jdbcTemplate.query(sql, (rs, rowNum) -> map(rs));
    }

    public SettlementRunEntity findById(long id) {
        String sql = "SELECT * FROM settlement_run WHERE id = ?";
        return jdbcTemplate.query(sql, ps -> ps.setLong(1, id),
            rs -> {
                if (rs.next()) {
                    return map(rs);
                } else {
                    throw new SettlementRunNotFoundException(id);
                }
            }
        );
    }

    public SettlementRunEntity findByLegId(long legId) {
        String sql = """
            select r.* from settlement_run r
            inner join settlement_leg l on r.id = l.settlement_run_id
            where l.id = ?
            """;
        return jdbcTemplate.query(sql, ps -> ps.setLong(1, legId),
            rs -> {
                if (rs.next()) {
                    return map(rs);
                } else {
                    throw new SettlementRunNotFoundException(legId);
                }
            }
        );
    }

    private List<SettlementLegEntity> supplyWithLegs(long runId, SettlementRunEntity.Builder builder) {
        List<SettlementLegEntity> legs = settlementLegRepository.findLegsBySettlementRunId(runId);
        builder.legs(new HashSet<>(legs));
        return legs;
    }

    private static Optional<Timestamp> getLatestSettledAtIfAllComplete(List<SettlementLegEntity> legs) {
        if (legs == null || legs.isEmpty()) {
            return Optional.empty();
        }

        boolean allHaveSettledAt = legs.stream()
            .allMatch(leg -> leg.settledAt() != null);

        if (!allHaveSettledAt) {
            return Optional.empty();
        }

        return legs.stream()
            .map(SettlementLegEntity::settledAt)
            .max(Timestamp::compareTo);
    }

    private void supplyWithAccounts(long runId, SettlementRunEntity.Builder builder) {
        List<String> venueAccounts = venueAccountRepository.findVenueAccountIdsBySettlementRunId(runId);
        builder.venueAccountIds(venueAccounts);
    }

    private SettlementRunEntity map(ResultSet rs) throws SQLException {
        long runId = rs.getLong("id");
        SettlementRunEntity.Builder builder = SettlementRunEntity.builder()
            .id(runId)
            .status(SettlementStatus.valueOf(rs.getString("status")))
            .startAt(rs.getDate("start_date"))
            .createdAt(rs.getTimestamp("created_at"));

        List<SettlementLegEntity> legs = supplyWithLegs(runId, builder);
        builder.settledAt(getLatestSettledAtIfAllComplete(legs).orElse(null));
        supplyWithAccounts(runId, builder);

        return builder.build();
    }

    public void clear() {
        jdbcTemplate.update("delete from settlement_run");
    }
}
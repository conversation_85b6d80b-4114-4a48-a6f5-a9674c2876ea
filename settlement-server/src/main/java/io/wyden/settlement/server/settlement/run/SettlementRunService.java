package io.wyden.settlement.server.settlement.run;

import io.wyden.settlement.client.run.SettlementRunResponse;
import io.wyden.settlement.server.settlement.MutationSubmittedResponse;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class SettlementRunService {

    private final SettlementRunRepository runRepository;
    private final SettlementRunResponseMapper mapper;

    public SettlementRunService(SettlementRunRepository runRepository, SettlementRunResponseMapper mapper) {
        this.runRepository = runRepository;
        this.mapper = mapper;
    }

    public List<SettlementRunResponse> findAllRunsInFinalState(LocalDate date) {
        return runRepository.findAllRunsInFinalState(date)
            .stream()
            .map(mapper::mapToDto)
            .toList();
    }

    public List<SettlementRunResponse> getRunsInNonFinalStatuses() {
        return runRepository.findAllRunsInNonFinalState()
            .stream()
            .map(mapper::mapToDto)
            .toList();
    }

    public SettlementRunResponse findById(long id) {
        SettlementRunEntity entity = runRepository.findById(id);
        return mapper.mapToDto(entity);
    }

    public SettlementRunResponse findByLegId(long legId) {
        SettlementRunEntity entity = runRepository.findByLegId(legId);
        return mapper.mapToDto(entity);
    }


}

# NG JIRA Guideline

> **Owner**:\
> Head of Product

> **Audience**:\
> Product Management, Project Management, Development, QA

> **Process Inputs**:\
> Specified tasks

> **Process Outputs**:\
> Signed-off development ready for release

# Table of Contents
[[_TOC_]]

## Process Diagram
```mermaid
---
title: JIRA Issue Workflow
---
flowchart TD
  1[New] --> 2[Ready for Dev]
  1 --> 3[Specifications in progress]
  3 --> 1
  3 --> 2
  2 --> 4[In progress]
  4 --> 5[Blocked]
  5 --> 4
  4 --> 6[In review]
  6 --> 7[In QA]
  7 --> 8[In BO]
  6 --> 4
  7 --> 4
  8 --> 4
  8 --> 10[Done]
```

## Issue statuses

| Status | Assigned to | Description |
|--------| ----------- | ----------- |
| New | - | The ticket is created. |
| Specifications in progress | BO | The initial specifications are unclear and under discussion. |
| Ready for Dev | - | The ticket is groomed: estimated, assigned to an epic, and has a BO assigned. |
| In progress | Developer | The ticket is assigned to a developer for work. |
| In review | - | Development is finished. The ticket is ready for peer review by another developer. |
| In review | Developer | The ticket is being reviewed by a developer. |
| Blocked | Developer | Development cannot continue. The reason for blockage must be specified. |
| In QA | QA engineer | The ticket is being tested by a QA engineer. |
| In BO | BO | The ticket is being reviewed by the business owner. |
| Done | - | Ticket closed. |

## Transition rules

### Specifications in progress
A ticket will remain in "Specifications in progress" until the description is clear enough for Development to groom the ticket. The ticket must be assigned to the business owner.

### Ready for Dev
A ticket will be set to "Ready for Dev" only when it is groomed. This means it is:
- Estimated
- Assigned to an epic
- Assigned to a BO

### In progress
A ticket in progress must have a developer assigned. A ticket can move from "In progress" to "In review" when:
- All DoD mentioned in the ticket are met

### Blocked
A ticket will be set to "Blocked" when development cannot continue. The reason for blocking the ticket must be stated either in a comment or as a linked, unresolved blocking ticket.

### In review
The ticket will be set to "In review" when the assigned developer has finished development and is confident it meets the specifications. A ticket in review will have a developer assigned for review.
- If the review is successful, the ticket will be moved to "In QA".
- If the review is not successful, the ticket will be moved back to "In progress" and reassigned to the developer who implemented it.

For a review to be successful, the following must apply:
- For GQL schema changes:
  - The change must be reviewed and approved by members of both the BE and FE teams.
  - The change must be backward compatible unless otherwise agreed.
- For architecture changes and published language changes (proto files):
  - The change must be additionally reviewed and approved by a Guild Of Architects team member.
  - The change must be backward compatible unless otherwise agreed.

Before moving the ticket to "In QA", the following must be done:
- All changes are fully merged to the main branch.
- All changes are deployed to the Dev environment.
- No regression was found by automated test runs on the Runner environment (can be triggered on demand or checked during nightly builds).

It is up to the agreement between the reviewer and implementer to ensure the above.

### In QA
A ticket in QA must have a QA engineer assigned, and the Tester field filled with the QA engineer performing the tests.
- If QA is successful, evidence of success must be indicated, and the ticket will be moved to "In BO".
- If QA fails, evidence of failure must be indicated, and the ticket will be moved to "In progress" and reassigned to the developer who implemented it.
- If the QA engineer estimates that individual QA for the ticket is not required, they must indicate so and move the ticket to "In BO".

In this phase, it is acceptable to manually check the quality of the change. The QA engineer should use this phase to detect missing automation coverage on the incoming change and:
- Raise follow-up QA tickets
- Document the new behavior in scenarios that must be manually tested before the release (until automation is in place)

### In BO
The business owner will validate the ticket by:
1. Ensuring that proper tests have been done by QA and the results are as expected.
2. Performing additional tests if necessary.

- If the business validation is successful, the ticket will be moved to "Done".
- If the business validation fails, evidence of failure must be indicated, and the ticket will be moved to "In progress" and reassigned to the developer who implemented it.
- If the BO estimates that business validation is not required for the individual ticket (e.g., a technical task), they must indicate so and move the ticket to "BO Accepted".

The BO is ultimately responsible for signing off a ticket to go into the release process.

### Done
Thanks! Next!